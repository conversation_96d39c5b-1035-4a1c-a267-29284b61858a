<template>
  <view class="container">
    <!-- 表单区域 -->
    <uni-forms ref="visitorForm" :model="formData" :rules="rules" label-width="100px">
      <uni-card title="访客基本信息" :is-shadow="false" margin="15px 0">
        <uni-forms-item label="姓名" name="name" required>
          <uni-easyinput
            v-model="formData.name"
            placeholder="请输入访客姓名"
            :clearable="true"
            @blur="validateField('name')"
          />
        </uni-forms-item>

        <uni-forms-item label="身份证号" name="idCard" required>
          <uni-easyinput
            v-model="formData.idCard"
            placeholder="请输入身份证号"
            :clearable="true"
            @blur="onIdCardBlur"
          />
        </uni-forms-item>

        <uni-forms-item label="联系电话" name="phone" required>
          <uni-easyinput
            v-model="formData.phone"
            placeholder="请输入联系电话"
            type="number"
            :clearable="true"
            @blur="validateField('phone')"
          />
        </uni-forms-item>

        <uni-forms-item label="所属公司" name="company">
          <uni-easyinput
            v-model="formData.company"
            placeholder="请输入所属公司"
            :clearable="true"
          />
        </uni-forms-item>
      </uni-card>

      <uni-card title="来访信息" :is-shadow="false" margin="15px 0">
        <uni-forms-item label="来访目的" name="purpose" required>
          <uni-data-checkbox
            v-model="formData.purpose"
            :localdata="purposeOptions"
            mode="button"
            @change="onPurposeChange"
          />
        </uni-forms-item>

        <uni-forms-item label="被访部门" name="visitToDept" required>
          <uni-easyinput
            v-model="formData.visitToDept"
            placeholder="请输入被访部门"
            :clearable="true"
            @blur="validateField('visitToDept')"
          />
        </uni-forms-item>

        <uni-forms-item label="被访人" name="visitTo" required>
          <uni-easyinput
            v-model="formData.visitTo"
            placeholder="请输入被访人姓名"
            :clearable="true"
            @blur="validateField('visitTo')"
          />
        </uni-forms-item>

        <uni-forms-item label="备注" name="remark">
          <uni-easyinput
            v-model="formData.remark"
            placeholder="请输入备注信息"
            type="textarea"
            :auto-height="true"
            :clearable="true"
          />
        </uni-forms-item>
      </uni-card>
    </uni-forms>

    <!-- 操作按钮 -->
    <view class="button-group">
      <button class="submit-btn" type="primary" @click="submitForm" :loading="submitting">
        {{ submitting ? '登记中...' : '确认登记' }}
      </button>
      <button class="reset-btn" @click="resetForm">重置表单</button>
    </view>
  </view>
</template>

<script>
import { addVisitor, getVisitorByIdCard } from '@/api/system/visitor'

export default {
  data() {
    return {
      submitting: false,
      formData: {
        name: '',
        idCard: '',
        phone: '',
        company: '',
        purpose: '',
        visitTo: '',
        visitToDept: '',
        remark: ''
      },
      // 来访目的选项
      purposeOptions: [
        { value: '商务洽谈', text: '商务洽谈' },
        { value: '技术交流', text: '技术交流' },
        { value: '参观访问', text: '参观访问' },
        { value: '会议参加', text: '会议参加' },
        { value: '培训学习', text: '培训学习' },
        { value: '维修服务', text: '维修服务' },
        { value: '其他', text: '其他' }
      ],
      // 表单验证规则
      rules: {
        name: {
          rules: [
            { required: true, errorMessage: '请输入访客姓名' },
            { minLength: 2, maxLength: 20, errorMessage: '姓名长度应在2-20个字符之间' }
          ]
        },
        idCard: {
          rules: [
            { required: true, errorMessage: '请输入身份证号' },
            {
              pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
              errorMessage: '请输入正确的身份证号码'
            }
          ]
        },
        phone: {
          rules: [
            { required: true, errorMessage: '请输入联系电话' },
            {
              pattern: /^1[3-9]\d{9}$/,
              errorMessage: '请输入正确的手机号码'
            }
          ]
        },
        purpose: {
          rules: [
            { required: true, errorMessage: '请选择来访目的' }
          ]
        },
        visitTo: {
          rules: [
            { required: true, errorMessage: '请输入被访人姓名' },
            { minLength: 2, maxLength: 20, errorMessage: '被访人姓名长度应在2-20个字符之间' }
          ]
        },
        visitToDept: {
          rules: [
            { required: true, errorMessage: '请输入被访部门' },
            { minLength: 2, maxLength: 50, errorMessage: '部门名称长度应在2-50个字符之间' }
          ]
        }
      }
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '来访人员登记'
    });
  },
  methods: {
    // 验证单个字段
    validateField(field) {
      this.$refs.visitorForm.validateField(field);
    },

    // 身份证号失焦事件
    async onIdCardBlur() {
      this.validateField('idCard');

      // 如果身份证号格式正确，尝试查询历史访客信息
      if (this.formData.idCard && this.formData.idCard.length === 18) {
        try {
          this.$modal.showLoading('查询访客信息...');
          const response = await getVisitorByIdCard(this.formData.idCard);

          if (response.code === 200 && response.data) {
            // 找到历史访客信息，询问是否自动填充
            const result = await this.$modal.confirm('发现该身份证号的历史访客记录，是否自动填充基本信息？');
            if (result) {
              this.fillVisitorInfo(response.data);
            }
          }
        } catch (error) {
          console.log('查询访客信息失败:', error);
          // 查询失败不影响正常流程，静默处理
        } finally {
          this.$modal.hideLoading();
        }
      }
    },

    // 填充访客信息
    fillVisitorInfo(visitorData) {
      this.formData.name = visitorData.name || '';
      this.formData.phone = visitorData.phone || '';
      this.formData.company = visitorData.company || '';
      this.$modal.showSuccess('已自动填充基本信息');
    },

    // 来访目的变更事件
    onPurposeChange(value) {
      console.log('来访目的变更:', value);
    },

    // 提交表单
    async submitForm() {
      try {
        // 表单验证
        const valid = await this.$refs.visitorForm.validate();
        if (!valid) {
          this.$modal.showError('请完善表单信息');
          return;
        }

        this.submitting = true;
        this.$modal.showLoading('正在登记...');

        // 准备提交数据
        const submitData = {
          ...this.formData,
          checkInTime: new Date().toISOString()
        };

        // 调用API提交数据
        const response = await addVisitor(submitData);

        if (response.code === 200) {
          this.$modal.showSuccess('访客登记成功！');

          // 询问是否打印访客凭证
          const printResult = await this.$modal.confirm('登记成功！是否需要打印访客凭证？');
          if (printResult) {
            this.printTicket(response.data.id);
          }

          // 重置表单
          this.resetForm();
        } else {
          this.$modal.showError(response.msg || '登记失败，请重试');
        }
      } catch (error) {
        console.error('提交表单失败:', error);
        this.$modal.showError('登记失败，请检查网络连接');
      } finally {
        this.submitting = false;
        this.$modal.hideLoading();
      }
    },

    // 打印访客凭证
    async printTicket(visitorId) {
      try {
        this.$modal.showLoading('准备打印...');
        // 这里可以调用打印API或跳转到打印页面
        // const response = await printVisitorTicket(visitorId);
        this.$modal.showSuccess('打印任务已发送');
      } catch (error) {
        console.error('打印失败:', error);
        this.$modal.showError('打印失败，请稍后重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        name: '',
        idCard: '',
        phone: '',
        company: '',
        purpose: '',
        visitTo: '',
        visitToDept: '',
        remark: ''
      };
      this.$refs.visitorForm.clearValidate();
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.button-group {
  margin-top: 30px;
  padding: 0 15px 30px;

  .submit-btn {
    width: 100%;
    height: 50px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;

    &:active {
      opacity: 0.8;
    }
  }

  .reset-btn {
    width: 100%;
    height: 45px;
    border-radius: 22px;
    font-size: 14px;
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;

    &:active {
      background-color: #e9ecef;
    }
  }
}

// 表单样式优化
:deep(.uni-forms-item) {
  margin-bottom: 20px;

  .uni-forms-item__label {
    font-weight: 500;
    color: #333;
  }

  .uni-forms-item__content {
    .uni-easyinput {
      .uni-easyinput__content {
        border-radius: 8px;
        border-color: #e0e0e0;

        &:focus-within {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }
    }
  }
}

// 卡片样式优化
:deep(.uni-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .uni-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;

    .uni-card__header-title {
      color: white;
      font-weight: bold;
    }
  }

  .uni-card__content {
    padding: 20px;
  }
}

// 数据选择器样式
:deep(.uni-data-checkbox) {
  .checklist-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .checklist-box {
      border-radius: 20px;
      border: 1px solid #e0e0e0;
      padding: 8px 16px;
      font-size: 14px;
      transition: all 0.3s ease;

      &.is--checked {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
      }

      &:not(.is--checked):active {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>