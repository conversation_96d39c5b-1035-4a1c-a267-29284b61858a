<template>
  <view class="container">
    <!-- 表单区域 -->
    <uni-forms ref="visitorForm" :model="formData" :rules="rules" label-width="100px">
      <!-- 主访客信息 -->
      <uni-card title="主访客信息" :is-shadow="false" margin="15px 0">
        <uni-forms-item label="姓名" name="mainVisitor.name" required>
          <uni-easyinput
            v-model="formData.mainVisitor.name"
            placeholder="请输入主访客姓名"
            :clearable="true"
            @blur="validateField('mainVisitor.name')"
          />
        </uni-forms-item>

        <uni-forms-item label="身份证号" name="mainVisitor.idCard" required>
          <uni-easyinput
            v-model="formData.mainVisitor.idCard"
            placeholder="请输入身份证号"
            :clearable="true"
            @blur="onMainVisitorIdCardBlur"
          />
        </uni-forms-item>

        <uni-forms-item label="联系电话" name="mainVisitor.phone" required>
          <uni-easyinput
            v-model="formData.mainVisitor.phone"
            placeholder="请输入联系电话"
            type="number"
            :clearable="true"
            @blur="validateField('mainVisitor.phone')"
          />
        </uni-forms-item>

        <uni-forms-item label="所属公司" name="mainVisitor.company">
          <uni-easyinput
            v-model="formData.mainVisitor.company"
            placeholder="请输入所属公司"
            :clearable="true"
          />
        </uni-forms-item>
      </uni-card>

      <!-- 随行人员 -->
      <uni-card title="随行人员" :is-shadow="false" margin="15px 0">
        <view class="companion-header">
          <text class="companion-count">共 {{ formData.companions.length }} 人</text>
          <button class="add-companion-btn" @click="addCompanion" size="mini" type="primary">
            <uni-icons type="plus" size="14"></uni-icons>
            添加随行人员
          </button>
        </view>

        <view v-if="formData.companions.length === 0" class="no-companions">
          <text class="no-companions-text">暂无随行人员</text>
        </view>

        <view v-for="(companion, index) in formData.companions" :key="index" class="companion-item">
          <view class="companion-header-item">
            <text class="companion-title">随行人员 {{ index + 1 }}</text>
            <button class="remove-companion-btn" @click="removeCompanion(index)" size="mini" type="warn">
              <uni-icons type="trash" size="12"></uni-icons>
            </button>
          </view>

          <uni-forms-item :label="`姓名`" :name="`companions.${index}.name`" required>
            <uni-easyinput
              v-model="companion.name"
              placeholder="请输入随行人员姓名"
              :clearable="true"
            />
          </uni-forms-item>

          <uni-forms-item :label="`身份证号`" :name="`companions.${index}.idCard`" required>
            <uni-easyinput
              v-model="companion.idCard"
              placeholder="请输入身份证号"
              :clearable="true"
              @blur="onCompanionIdCardBlur(companion, index)"
            />
          </uni-forms-item>

          <uni-forms-item :label="`联系电话`" :name="`companions.${index}.phone`">
            <uni-easyinput
              v-model="companion.phone"
              placeholder="请输入联系电话"
              type="number"
              :clearable="true"
            />
          </uni-forms-item>
        </view>
      </uni-card>

      <!-- 来访信息 -->
      <uni-card title="来访信息" :is-shadow="false" margin="15px 0">
        <uni-forms-item label="来访目的" name="visitInfo.purpose" required>
          <uni-data-checkbox
            v-model="formData.visitInfo.purpose"
            :localdata="purposeOptions"
            mode="button"
            @change="onPurposeChange"
          />
        </uni-forms-item>

        <uni-forms-item label="被访部门" name="visitInfo.visitToDept" required>
          <uni-easyinput
            v-model="formData.visitInfo.visitToDept"
            placeholder="请输入被访部门"
            :clearable="true"
            @blur="validateField('visitInfo.visitToDept')"
          />
        </uni-forms-item>

        <uni-forms-item label="被访人" name="visitInfo.visitTo" required>
          <uni-easyinput
            v-model="formData.visitInfo.visitTo"
            placeholder="请输入被访人姓名"
            :clearable="true"
            @blur="validateField('visitInfo.visitTo')"
          />
        </uni-forms-item>

        <uni-forms-item label="预计访问时间" name="visitInfo.expectedDuration">
          <uni-data-checkbox
            v-model="formData.visitInfo.expectedDuration"
            :localdata="durationOptions"
            mode="button"
          />
        </uni-forms-item>

        <uni-forms-item label="备注" name="visitInfo.remark">
          <uni-easyinput
            v-model="formData.visitInfo.remark"
            placeholder="请输入备注信息"
            type="textarea"
            :auto-height="true"
            :clearable="true"
          />
        </uni-forms-item>
      </uni-card>
    </uni-forms>

    <!-- 操作按钮 -->
    <view class="button-group">
      <button class="submit-btn" type="primary" @click="submitForm" :loading="submitting">
        {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}
      </button>
      <button class="reset-btn" @click="resetForm">重置表单</button>
    </view>
  </view>
</template>

<script>
import { submitVisitorRegistration, getVisitorByIdCard } from '@/api/system/visitor'

export default {
  data() {
    return {
      submitting: false,
      formData: {
        // 主访客信息
        mainVisitor: {
          name: '',
          idCard: '',
          phone: '',
          company: ''
        },
        // 随行人员列表
        companions: [],
        // 来访信息
        visitInfo: {
          purpose: '',
          visitTo: '',
          visitToDept: '',
          expectedDuration: '2小时',
          remark: ''
        }
      },
      // 来访目的选项
      purposeOptions: [
        { value: '商务洽谈', text: '商务洽谈' },
        { value: '技术交流', text: '技术交流' },
        { value: '参观访问', text: '参观访问' },
        { value: '会议参加', text: '会议参加' },
        { value: '培训学习', text: '培训学习' },
        { value: '维修服务', text: '维修服务' },
        { value: '供应商拜访', text: '供应商拜访' },
        { value: '客户拜访', text: '客户拜访' },
        { value: '其他', text: '其他' }
      ],
      // 预计访问时长选项
      durationOptions: [
        { value: '1小时', text: '1小时' },
        { value: '2小时', text: '2小时' },
        { value: '半天', text: '半天' },
        { value: '1天', text: '1天' },
        { value: '其他', text: '其他' }
      ],
      // 表单验证规则
      rules: {
        'mainVisitor.name': {
          rules: [
            { required: true, errorMessage: '请输入主访客姓名' },
            { minLength: 2, maxLength: 20, errorMessage: '姓名长度应在2-20个字符之间' }
          ]
        },
        'mainVisitor.idCard': {
          rules: [
            { required: true, errorMessage: '请输入身份证号' },
            {
              pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
              errorMessage: '请输入正确的身份证号码'
            }
          ]
        },
        'mainVisitor.phone': {
          rules: [
            { required: true, errorMessage: '请输入联系电话' },
            {
              pattern: /^1[3-9]\d{9}$/,
              errorMessage: '请输入正确的手机号码'
            }
          ]
        },
        'visitInfo.purpose': {
          rules: [
            { required: true, errorMessage: '请选择来访目的' }
          ]
        },
        'visitInfo.visitTo': {
          rules: [
            { required: true, errorMessage: '请输入被访人姓名' },
            { minLength: 2, maxLength: 20, errorMessage: '被访人姓名长度应在2-20个字符之间' }
          ]
        },
        'visitInfo.visitToDept': {
          rules: [
            { required: true, errorMessage: '请输入被访部门' },
            { minLength: 2, maxLength: 50, errorMessage: '部门名称长度应在2-50个字符之间' }
          ]
        }
      }
    };
  },
  computed: {
    // 总访客人数
    totalVisitors() {
      return 1 + this.formData.companions.length;
    }
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '来访人员登记'
    });
  },
  methods: {
    // 验证单个字段
    validateField(field) {
      this.$refs.visitorForm.validateField(field);
    },

    // 主访客身份证号失焦事件
    async onMainVisitorIdCardBlur() {
      this.validateField('mainVisitor.idCard');

      // 如果身份证号格式正确，尝试查询历史访客信息
      if (this.formData.mainVisitor.idCard && this.formData.mainVisitor.idCard.length === 18) {
        try {
          this.$modal.showLoading('查询访客信息...');
          const response = await getVisitorByIdCard(this.formData.mainVisitor.idCard);

          if (response.code === 200 && response.data) {
            // 找到历史访客信息，询问是否自动填充
            const result = await this.$modal.confirm('发现该身份证号的历史访客记录，是否自动填充基本信息？');
            if (result) {
              this.fillMainVisitorInfo(response.data);
            }
          }
        } catch (error) {
          console.log('查询访客信息失败:', error);
          // 查询失败不影响正常流程，静默处理
        } finally {
          this.$modal.hideLoading();
        }
      }
    },

    // 随行人员身份证号失焦事件
    async onCompanionIdCardBlur(companion, index) {
      if (companion.idCard && companion.idCard.length === 18) {
        try {
          this.$modal.showLoading('查询访客信息...');
          const response = await getVisitorByIdCard(companion.idCard);

          if (response.code === 200 && response.data) {
            // 找到历史访客信息，询问是否自动填充
            const result = await this.$modal.confirm(`发现随行人员${index + 1}的历史访客记录，是否自动填充基本信息？`);
            if (result) {
              this.fillCompanionInfo(companion, response.data);
            }
          }
        } catch (error) {
          console.log('查询随行人员信息失败:', error);
          // 查询失败不影响正常流程，静默处理
        } finally {
          this.$modal.hideLoading();
        }
      }
    },

    // 填充主访客信息
    fillMainVisitorInfo(visitorData) {
      this.formData.mainVisitor.name = visitorData.name || '';
      this.formData.mainVisitor.phone = visitorData.phone || '';
      this.formData.mainVisitor.company = visitorData.company || '';
      this.$modal.showSuccess('已自动填充主访客基本信息');
    },

    // 填充随行人员信息
    fillCompanionInfo(companion, visitorData) {
      companion.name = visitorData.name || '';
      companion.phone = visitorData.phone || '';
      this.$modal.showSuccess('已自动填充随行人员基本信息');
    },

    // 添加随行人员
    addCompanion() {
      if (this.formData.companions.length >= 10) {
        this.$modal.showError('最多只能添加10名随行人员');
        return;
      }

      this.formData.companions.push({
        name: '',
        idCard: '',
        phone: ''
      });

      this.$modal.showSuccess('已添加随行人员');
    },

    // 移除随行人员
    removeCompanion(index) {
      this.formData.companions.splice(index, 1);
      this.$modal.showSuccess('已移除随行人员');
    },

    // 来访目的变更事件
    onPurposeChange(value) {
      console.log('来访目的变更:', value);
    },

    // 提交表单
    async submitForm() {
      try {
        // 表单验证
        const valid = await this.$refs.visitorForm.validate();
        if (!valid) {
          this.$modal.showError('请完善表单信息');
          return;
        }

        // 验证随行人员信息
        if (!this.validateCompanions()) {
          return;
        }

        this.submitting = true;
        this.$modal.showLoading(`正在登记${this.totalVisitors}名访客...`);

        // 准备提交数据 - 按照PC端的数据结构
        const submitData = {
          // 主访客作为第一个访客
          visitors: [
            {
              name: this.formData.mainVisitor.name,
              idCard: this.formData.mainVisitor.idCard,
              phone: this.formData.mainVisitor.phone,
              company: this.formData.mainVisitor.company,
              isMainVisitor: true
            },
            // 添加所有随行人员
            ...this.formData.companions.map(companion => ({
              name: companion.name,
              idCard: companion.idCard,
              phone: companion.phone,
              company: this.formData.mainVisitor.company, // 随行人员使用主访客的公司
              isMainVisitor: false
            }))
          ],
          // 来访信息
          visitInfo: {
            purpose: this.formData.visitInfo.purpose,
            visitTo: this.formData.visitInfo.visitTo,
            visitToDept: this.formData.visitInfo.visitToDept,
            expectedDuration: this.formData.visitInfo.expectedDuration,
            remark: this.formData.visitInfo.remark,
            expectedVisitDate: new Date().toISOString().split('T')[0], // 今天
            expectedVisitTime: new Date().toTimeString().split(' ')[0].substring(0, 5) // 当前时间
          }
        };

        // 调用API提交数据
        const response = await submitVisitorRegistration(submitData);

        if (response.code === 200) {
          this.$modal.showSuccess(`${this.totalVisitors}名访客登记成功！`);

          // 询问是否打印访客凭证
          const printResult = await this.$modal.confirm(`登记成功！是否需要打印${this.totalVisitors}名访客的凭证？`);
          if (printResult) {
            this.printTickets(response.data);
          }

          // 重置表单
          this.resetForm();
        } else {
          this.$modal.showError(response.msg || '登记失败，请重试');
        }
      } catch (error) {
        console.error('提交表单失败:', error);
        this.$modal.showError('登记失败，请检查网络连接');
      } finally {
        this.submitting = false;
        this.$modal.hideLoading();
      }
    },

    // 验证随行人员信息
    validateCompanions() {
      for (let i = 0; i < this.formData.companions.length; i++) {
        const companion = this.formData.companions[i];

        if (!companion.name || companion.name.trim() === '') {
          this.$modal.showError(`请输入随行人员${i + 1}的姓名`);
          return false;
        }

        if (!companion.idCard || companion.idCard.trim() === '') {
          this.$modal.showError(`请输入随行人员${i + 1}的身份证号`);
          return false;
        }

        // 验证身份证号格式
        const idCardPattern = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
        if (!idCardPattern.test(companion.idCard)) {
          this.$modal.showError(`随行人员${i + 1}的身份证号格式不正确`);
          return false;
        }

        // 检查身份证号是否重复
        if (companion.idCard === this.formData.mainVisitor.idCard) {
          this.$modal.showError(`随行人员${i + 1}的身份证号不能与主访客相同`);
          return false;
        }

        for (let j = 0; j < this.formData.companions.length; j++) {
          if (i !== j && companion.idCard === this.formData.companions[j].idCard) {
            this.$modal.showError(`随行人员${i + 1}与随行人员${j + 1}的身份证号重复`);
            return false;
          }
        }
      }

      return true;
    },

    // 打印访客凭证
    async printTickets(registrationData) {
      try {
        this.$modal.showLoading('准备打印...');
        // 这里可以调用打印API或跳转到打印页面
        // 可以根据registrationData中的访客信息批量打印
        this.$modal.showSuccess('打印任务已发送');
      } catch (error) {
        console.error('打印失败:', error);
        this.$modal.showError('打印失败，请稍后重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        // 主访客信息
        mainVisitor: {
          name: '',
          idCard: '',
          phone: '',
          company: ''
        },
        // 随行人员列表
        companions: [],
        // 来访信息
        visitInfo: {
          purpose: '',
          visitTo: '',
          visitToDept: '',
          expectedDuration: '2小时',
          remark: ''
        }
      };
      this.$refs.visitorForm.clearValidate();
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.button-group {
  margin-top: 30px;
  padding: 0 15px 30px;

  .submit-btn {
    width: 100%;
    height: 50px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;

    &:active {
      opacity: 0.8;
    }
  }

  .reset-btn {
    width: 100%;
    height: 45px;
    border-radius: 22px;
    font-size: 14px;
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;

    &:active {
      background-color: #e9ecef;
    }
  }
}

// 表单样式优化
:deep(.uni-forms-item) {
  margin-bottom: 20px;

  .uni-forms-item__label {
    font-weight: 500;
    color: #333;
  }

  .uni-forms-item__content {
    .uni-easyinput {
      .uni-easyinput__content {
        border-radius: 8px;
        border-color: #e0e0e0;

        &:focus-within {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }
    }
  }
}

// 卡片样式优化
:deep(.uni-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .uni-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;

    .uni-card__header-title {
      color: white;
      font-weight: bold;
    }
  }

  .uni-card__content {
    padding: 20px;
  }
}

// 随行人员样式
.companion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .companion-count {
    font-size: 14px;
    color: #666;
  }

  .add-companion-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 12px;
    background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
    border: none;
    color: white;

    &:active {
      opacity: 0.8;
    }
  }
}

.no-companions {
  text-align: center;
  padding: 40px 20px;

  .no-companions-text {
    color: #999;
    font-size: 14px;
  }
}

.companion-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fafafa;

  .companion-header-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .companion-title {
      font-weight: bold;
      color: #333;
      font-size: 14px;
    }

    .remove-companion-btn {
      padding: 4px 8px;
      border-radius: 10px;
      font-size: 10px;
      background-color: #F56C6C;
      border: none;
      color: white;
      display: flex;
      align-items: center;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

// 数据选择器样式
:deep(.uni-data-checkbox) {
  .checklist-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .checklist-box {
      border-radius: 20px;
      border: 1px solid #e0e0e0;
      padding: 8px 16px;
      font-size: 14px;
      transition: all 0.3s ease;

      &.is--checked {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
      }

      &:not(.is--checked):active {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>