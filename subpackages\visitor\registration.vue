<template>
  <view class="container">
    <!-- 表单区域 -->
    <uni-forms ref="visitorForm" :model="formData" :rules="rules" label-width="100px">
      <!-- 访问信息 -->
      <uni-card title="访问信息" :is-shadow="false" margin="15px 0">
        <uni-forms-item label="来访事由" name="visitInfo.purpose" required>
          <uni-easyinput
            v-model="formData.visitInfo.purpose"
            placeholder="请输入来访事由"
            type="textarea"
            :auto-height="true"
            :clearable="true"
            maxlength="200"
            @blur="validateField('visitInfo.purpose')"
          />
        </uni-forms-item>

        <uni-forms-item label="被访人姓名" name="visitInfo.visitTo" required>
          <uni-easyinput
            v-model="formData.visitInfo.visitTo"
            placeholder="请输入被访人姓名"
            :clearable="true"
            @blur="validateField('visitInfo.visitTo')"
          />
        </uni-forms-item>

        <uni-forms-item label="被访部门" name="visitInfo.visitToDept" required>
          <uni-easyinput
            v-model="formData.visitInfo.visitToDept"
            placeholder="请输入被访部门"
            :clearable="true"
            @blur="validateField('visitInfo.visitToDept')"
          />
        </uni-forms-item>

        <uni-forms-item label="车牌号" name="visitInfo.vehicleNumber">
          <uni-easyinput
            v-model="formData.visitInfo.vehicleNumber"
            placeholder="如有车辆请填写车牌号，多个用逗号分隔"
            :clearable="true"
          />
        </uni-forms-item>

        <uni-forms-item label="预计来访时间" name="visitInfo.expectedArrivalTime" required>
          <uni-datetime-picker
            v-model="formData.visitInfo.expectedArrivalTime"
            type="datetime"
            :clear-icon="false"
            placeholder="选择预计来访时间"
            @change="onArrivalTimeChange"
          />
        </uni-forms-item>

        <uni-forms-item label="预计离开时间" name="visitInfo.expectedDepartureTime" required>
          <uni-datetime-picker
            v-model="formData.visitInfo.expectedDepartureTime"
            type="datetime"
            :clear-icon="false"
            placeholder="选择预计离开时间"
            @change="onDepartureTimeChange"
          />
        </uni-forms-item>
      </uni-card>

      <!-- 访客信息 -->
      <uni-card title="访客信息" :is-shadow="false" margin="15px 0">
        <view class="visitor-header">
          <text class="visitor-count">访客列表（第一位为主联系人）共 {{ formData.visitors.length }} 人</text>
          <button class="add-visitor-btn" @click="addVisitor" size="mini" type="primary">
            <uni-icons type="plus" size="14"></uni-icons>
            添加访客
          </button>
        </view>

        <view v-for="(visitor, index) in formData.visitors" :key="index" class="visitor-item">
          <view class="visitor-header-item">
            <text class="visitor-title">
              {{ index === 0 ? '主联系人' : `访客 ${index + 1}` }}
            </text>
            <button
              v-if="index > 0"
              class="remove-visitor-btn"
              @click="removeVisitor(index)"
              size="mini"
              type="warn"
            >
              <uni-icons type="trash" size="12"></uni-icons>
            </button>
          </view>

          <uni-forms-item :label="`姓名`" :name="`visitors.${index}.name`" required>
            <uni-easyinput
              v-model="visitor.name"
              placeholder="请输入姓名"
              :clearable="true"
              @blur="validateVisitorField(index, 'name')"
            />
          </uni-forms-item>

          <uni-forms-item :label="`手机号`" :name="`visitors.${index}.phone`" required>
            <uni-easyinput
              v-model="visitor.phone"
              placeholder="请输入手机号"
              type="number"
              :clearable="true"
              @blur="validateVisitorField(index, 'phone')"
            />
          </uni-forms-item>

          <uni-forms-item :label="`身份证或护照号`" :name="`visitors.${index}.idCard`" required>
            <uni-easyinput
              v-model="visitor.idCard"
              placeholder="请输入身份证或护照号"
              :clearable="true"
              @blur="onVisitorIdCardBlur(visitor, index)"
            />
          </uni-forms-item>

          <uni-forms-item :label="`公司名称`" :name="`visitors.${index}.company`">
            <uni-easyinput
              v-model="visitor.company"
              placeholder="请输入公司名称"
              :clearable="true"
            />
          </uni-forms-item>
        </view>
      </uni-card>
    </uni-forms>

    <!-- 操作按钮 -->
    <view class="button-group">
      <button class="submit-btn" type="primary" @click="submitForm" :loading="submitting">
        {{ submitting ? '登记中...' : `确认登记 (${totalVisitors}人)` }}
      </button>
      <button class="reset-btn" @click="resetForm">重置表单</button>
    </view>
  </view>
</template>

<script>
import { submitVisitorRegistration, getVisitorByIdCard } from '@/api/system/visitor'

export default {
  data() {
    return {
      submitting: false,
      formData: {
        // 访客列表（第一位为主联系人）
        visitors: [
          {
            name: '',
            phone: '',
            idCard: '',
            company: '',
            isMainContact: true
          }
        ],
        // 来访信息
        visitInfo: {
          purpose: '',
          visitTo: '',
          visitToDept: '',
          vehicleNumber: '',
          expectedArrivalTime: '',
          expectedDepartureTime: ''
        }
      },

      // 表单验证规则
      rules: {
        'visitInfo.purpose': {
          rules: [
            { required: true, errorMessage: '请输入来访事由' },
            { minLength: 2, maxLength: 200, errorMessage: '来访事由长度应在2-200个字符之间' }
          ]
        },
        'visitInfo.visitTo': {
          rules: [
            { required: true, errorMessage: '请输入被访人姓名' },
            { minLength: 2, maxLength: 20, errorMessage: '被访人姓名长度应在2-20个字符之间' }
          ]
        },
        'visitInfo.visitToDept': {
          rules: [
            { required: true, errorMessage: '请输入被访部门' },
            { minLength: 2, maxLength: 50, errorMessage: '部门名称长度应在2-50个字符之间' }
          ]
        },
        'visitInfo.expectedArrivalTime': {
          rules: [
            { required: true, errorMessage: '请选择预计来访时间' }
          ]
        },
        'visitInfo.expectedDepartureTime': {
          rules: [
            { required: true, errorMessage: '请选择预计离开时间' }
          ]
        }
      }
    };
  },
  computed: {
    // 总访客人数
    totalVisitors() {
      return this.formData.visitors.length;
    }
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '来访人员登记'
    });
  },
  methods: {
    // 验证单个字段
    validateField(field) {
      this.$refs.visitorForm.validateField(field);
    },

    // 验证访客字段
    validateVisitorField(index, field) {
      this.$refs.visitorForm.validateField(`visitors.${index}.${field}`);
    },

    // 访客身份证号失焦事件
    async onVisitorIdCardBlur(visitor, index) {
      this.validateVisitorField(index, 'idCard');

      // 如果身份证号格式正确，尝试查询历史访客信息
      if (visitor.idCard && visitor.idCard.length >= 15) {
        try {
          this.$modal.showLoading('查询访客信息...');
          const response = await getVisitorByIdCard(visitor.idCard);

          if (response.code === 200 && response.data) {
            // 找到历史访客信息，询问是否自动填充
            const visitorTitle = index === 0 ? '主联系人' : `访客${index + 1}`;
            const result = await this.$modal.confirm(`发现${visitorTitle}的历史访客记录，是否自动填充基本信息？`);
            if (result) {
              this.fillVisitorInfo(visitor, response.data);
            }
          }
        } catch (error) {
          console.log('查询访客信息失败:', error);
          // 查询失败不影响正常流程，静默处理
        } finally {
          this.$modal.hideLoading();
        }
      }
    },

    // 填充访客信息
    fillVisitorInfo(visitor, visitorData) {
      visitor.name = visitorData.name || '';
      visitor.phone = visitorData.phone || '';
      visitor.company = visitorData.company || '';
      this.$modal.showSuccess('已自动填充访客基本信息');
    },

    // 添加访客
    addVisitor() {
      if (this.formData.visitors.length >= 10) {
        this.$modal.showError('最多只能添加10名访客');
        return;
      }

      this.formData.visitors.push({
        name: '',
        phone: '',
        idCard: '',
        company: '',
        isMainContact: false
      });

      this.$modal.showSuccess('已添加访客');
    },

    // 移除访客
    removeVisitor(index) {
      if (index === 0) {
        this.$modal.showError('不能删除主联系人');
        return;
      }

      this.formData.visitors.splice(index, 1);
      this.$modal.showSuccess('已移除访客');
    },

    // 来访时间变更事件
    onArrivalTimeChange(value) {
      console.log('预计来访时间变更:', value);
      // 自动设置离开时间为来访时间后4小时
      if (value && !this.formData.visitInfo.expectedDepartureTime) {
        const arrivalTime = new Date(value);
        const departureTime = new Date(arrivalTime.getTime() + 4 * 60 * 60 * 1000);
        this.formData.visitInfo.expectedDepartureTime = departureTime.toISOString();
      }
    },

    // 离开时间变更事件
    onDepartureTimeChange(value) {
      console.log('预计离开时间变更:', value);
      // 验证离开时间不能早于来访时间
      if (value && this.formData.visitInfo.expectedArrivalTime) {
        const arrivalTime = new Date(this.formData.visitInfo.expectedArrivalTime);
        const departureTime = new Date(value);

        if (departureTime <= arrivalTime) {
          this.$modal.showError('预计离开时间不能早于或等于来访时间');
          this.formData.visitInfo.expectedDepartureTime = '';
        }
      }
    },

    // 提交表单
    async submitForm() {
      try {
        // 表单验证
        const valid = await this.$refs.visitorForm.validate();
        if (!valid) {
          this.$modal.showError('请完善表单信息');
          return;
        }

        // 验证访客信息
        if (!this.validateVisitors()) {
          return;
        }

        // 验证时间
        if (!this.validateTimes()) {
          return;
        }

        this.submitting = true;
        this.$modal.showLoading(`正在登记${this.totalVisitors}名访客...`);

        // 准备提交数据 - 按照PC端的数据结构
        const submitData = {
          // 访客列表
          visitors: this.formData.visitors.map((visitor, index) => ({
            name: visitor.name,
            phone: visitor.phone,
            idCard: visitor.idCard,
            company: visitor.company,
            isMainContact: index === 0
          })),
          // 来访信息
          visitInfo: {
            purpose: this.formData.visitInfo.purpose,
            visitTo: this.formData.visitInfo.visitTo,
            visitToDept: this.formData.visitInfo.visitToDept,
            vehicleNumber: this.formData.visitInfo.vehicleNumber,
            expectedArrivalTime: this.formData.visitInfo.expectedArrivalTime,
            expectedDepartureTime: this.formData.visitInfo.expectedDepartureTime
          }
        };

        // 调用API提交数据
        const response = await submitVisitorRegistration(submitData);

        if (response.code === 200) {
          this.$modal.showSuccess(`${this.totalVisitors}名访客登记成功！`);

          // 询问是否打印访客凭证
          const printResult = await this.$modal.confirm(`登记成功！是否需要打印${this.totalVisitors}名访客的凭证？`);
          if (printResult) {
            this.printTickets(response.data);
          }

          // 重置表单
          this.resetForm();
        } else {
          this.$modal.showError(response.msg || '登记失败，请重试');
        }
      } catch (error) {
        console.error('提交表单失败:', error);
        this.$modal.showError('登记失败，请检查网络连接');
      } finally {
        this.submitting = false;
        this.$modal.hideLoading();
      }
    },

    // 验证访客信息
    validateVisitors() {
      for (let i = 0; i < this.formData.visitors.length; i++) {
        const visitor = this.formData.visitors[i];
        const visitorTitle = i === 0 ? '主联系人' : `访客${i + 1}`;

        if (!visitor.name || visitor.name.trim() === '') {
          this.$modal.showError(`请输入${visitorTitle}的姓名`);
          return false;
        }

        if (!visitor.phone || visitor.phone.trim() === '') {
          this.$modal.showError(`请输入${visitorTitle}的手机号`);
          return false;
        }

        if (!visitor.idCard || visitor.idCard.trim() === '') {
          this.$modal.showError(`请输入${visitorTitle}的身份证或护照号`);
          return false;
        }

        // 验证手机号格式
        const phonePattern = /^1[3-9]\d{9}$/;
        if (!phonePattern.test(visitor.phone)) {
          this.$modal.showError(`${visitorTitle}的手机号格式不正确`);
          return false;
        }

        // 检查身份证号是否重复
        for (let j = 0; j < this.formData.visitors.length; j++) {
          if (i !== j && visitor.idCard === this.formData.visitors[j].idCard) {
            const otherTitle = j === 0 ? '主联系人' : `访客${j + 1}`;
            this.$modal.showError(`${visitorTitle}与${otherTitle}的身份证号重复`);
            return false;
          }
        }
      }

      return true;
    },

    // 验证时间
    validateTimes() {
      if (!this.formData.visitInfo.expectedArrivalTime) {
        this.$modal.showError('请选择预计来访时间');
        return false;
      }

      if (!this.formData.visitInfo.expectedDepartureTime) {
        this.$modal.showError('请选择预计离开时间');
        return false;
      }

      const arrivalTime = new Date(this.formData.visitInfo.expectedArrivalTime);
      const departureTime = new Date(this.formData.visitInfo.expectedDepartureTime);
      const now = new Date();

      if (arrivalTime <= now) {
        this.$modal.showError('预计来访时间不能早于当前时间');
        return false;
      }

      if (departureTime <= arrivalTime) {
        this.$modal.showError('预计离开时间不能早于或等于来访时间');
        return false;
      }

      return true;
    },

    // 打印访客凭证
    async printTickets(registrationData) {
      try {
        this.$modal.showLoading('准备打印...');
        // 这里可以调用打印API或跳转到打印页面
        // 可以根据registrationData中的访客信息批量打印
        this.$modal.showSuccess('打印任务已发送');
      } catch (error) {
        console.error('打印失败:', error);
        this.$modal.showError('打印失败，请稍后重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 重置表单
    resetForm() {
      this.formData = {
        // 访客列表（第一位为主联系人）
        visitors: [
          {
            name: '',
            phone: '',
            idCard: '',
            company: '',
            isMainContact: true
          }
        ],
        // 来访信息
        visitInfo: {
          purpose: '',
          visitTo: '',
          visitToDept: '',
          vehicleNumber: '',
          expectedArrivalTime: '',
          expectedDepartureTime: ''
        }
      };
      this.$refs.visitorForm.clearValidate();
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.button-group {
  margin-top: 30px;
  padding: 0 15px 30px;

  .submit-btn {
    width: 100%;
    height: 50px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;

    &:active {
      opacity: 0.8;
    }
  }

  .reset-btn {
    width: 100%;
    height: 45px;
    border-radius: 22px;
    font-size: 14px;
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;

    &:active {
      background-color: #e9ecef;
    }
  }
}

// 表单样式优化
:deep(.uni-forms-item) {
  margin-bottom: 20px;

  .uni-forms-item__label {
    font-weight: 500;
    color: #333;
  }

  .uni-forms-item__content {
    .uni-easyinput {
      .uni-easyinput__content {
        border-radius: 8px;
        border-color: #e0e0e0;

        &:focus-within {
          border-color: #667eea;
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
        }
      }
    }
  }
}

// 卡片样式优化
:deep(.uni-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .uni-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;

    .uni-card__header-title {
      color: white;
      font-weight: bold;
    }
  }

  .uni-card__content {
    padding: 20px;
  }
}

// 访客样式
.visitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .visitor-count {
    font-size: 14px;
    color: #666;
    flex: 1;
  }

  .add-visitor-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border-radius: 15px;
    font-size: 12px;
    background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
    border: none;
    color: white;

    &:active {
      opacity: 0.8;
    }
  }
}

.visitor-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fafafa;

  &:first-child {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    border-color: #667eea;
  }

  .visitor-header-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .visitor-title {
      font-weight: bold;
      color: #333;
      font-size: 14px;

      &:first-child {
        color: #667eea;
      }
    }

    .remove-visitor-btn {
      padding: 4px 8px;
      border-radius: 10px;
      font-size: 10px;
      background-color: #F56C6C;
      border: none;
      color: white;
      display: flex;
      align-items: center;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

// 数据选择器样式
:deep(.uni-data-checkbox) {
  .checklist-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;

    .checklist-box {
      border-radius: 20px;
      border: 1px solid #e0e0e0;
      padding: 8px 16px;
      font-size: 14px;
      transition: all 0.3s ease;

      &.is--checked {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
      }

      &:not(.is--checked):active {
        background-color: #f8f9fa;
      }
    }
  }
}
</style>