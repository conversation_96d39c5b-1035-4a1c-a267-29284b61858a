export default {
  // 消息提示
  msg(content) {
    uni.showToast({
      title: content,
      icon: 'none',
      duration: 3000  // 增加显示时间到3秒
    })
  },
  // 错误消息
  msgError(content) {
    uni.showModal({
      title: '提示',
      content: content,
      showCancel: false
    })
  },
  // 成功消息
  msgSuccess(content) {
    uni.showToast({
      title: content,
      icon: 'success'
    })
  },
  // 隐藏消息
  hideMsg(content) {
    uni.hideToast()
  },
  // 弹出提示
  alert(content) {
    uni.showModal({
      title: '提示',
      content: content,
      showCancel: false
    })
  },
  // 确认窗体
  confirm(content) {
    return new Promise((resolve, reject) => {
      uni.showModal({
        title: '系统提示',
        content: content,
        cancelText: '取消',
        confirmText: '确定',
        success: function(res) {
          if (res.confirm) {
            resolve(res.confirm)
          }
        }
      })
    })
  },
  // 提示信息
  showToast(option) {
    if (typeof option === "object") {
      uni.showToast(option)
    } else {
      uni.showToast({
        title: option,
        icon: "none",
        duration: 2500
      })
    }
  },
  // 打开遮罩层
  loading(content) {
    uni.showLoading({
      title: content,
      icon: 'none'
    })
  },
  // 关闭遮罩层
  closeLoading() {
    uni.hideLoading()
  },
  // 显示加载提示 (别名方法)
  showLoading(content) {
    uni.showLoading({
      title: content || '加载中...',
      mask: true
    })
  },
  // 隐藏加载提示 (别名方法)
  hideLoading() {
    uni.hideLoading()
  },
  // 成功提示
  showSuccess(content) {
    uni.showToast({
      title: content,
      icon: 'success',
      duration: 2000
    })
  },
  // 错误提示
  showError(content) {
    uni.showToast({
      title: content,
      icon: 'error',
      duration: 2500
    })
  }
}
