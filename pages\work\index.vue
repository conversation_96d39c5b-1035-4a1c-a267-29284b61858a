<template>
  <view class="work-container">
    <!-- 欢迎横幅 -->
    <view class="welcome-banner">
      <view class="banner-content">
        <view class="banner-title">综合管理工作台</view>
        <!-- <view class="banner-subtitle">选择您要使用的功能模块</view> -->
      </view>
    </view>

    <!-- 功能模块网格 -->
    <view class="modules-container">
      <view class="modules-grid">
        <!-- 能源管理 -->
        <view class="module-item" @click="gotoEms"  v-if="checkPermi(['asc:ems:list'])">
          <view class="module-icon">
            <uni-icons type="bars" size="32" color="#67C23A"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">能源管理</text>
            <text class="module-desc">实时数据监控</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 车辆管理 -->
        <view class="module-item" @click="gotoVehicle" v-if="checkPermi(['asc:vehicle:list'])">
          <view class="module-icon">
            <uni-icons type="compose" size="32" color="#409EFF"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">车辆管理</text>
            <text class="module-desc">车辆信息、车牌机管理</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 访客管理 -->
        <view class="module-item" @click="gotoVisitor" v-if="checkPermi(['asc:visitor:list'])">
          <view class="module-icon">
            <uni-icons type="person-add" size="32" color="#F56C6C"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">访客管理</text>
            <text class="module-desc">访客登记管理</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 数据图表 -->
        <view class="module-item" @click="gotoCharts" v-if="checkPermi(['ems:visitor:list'])">
          <view class="module-icon">
            <uni-icons type="chart" size="32" color="#9C27B0"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">数据图表</text>
            <text class="module-desc">数据可视化分析</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { checkPermi } from '@/utils/permission';

export default {
  data() {
    return {
      deviceList: []
    }
  },
  computed: {
    hasVehiclePermission() {
      const vehiclePermi = this.checkPermi(['asc:vehicle:list']);
      const devicePermi = this.checkPermi(['asc:device:manage']);
      const result = vehiclePermi || devicePermi;

      console.log('=== 车辆管理权限检查 ===');
      console.log('车辆权限(asc:vehicle:list):', vehiclePermi);
      console.log('设备权限(asc:device:manage):', devicePermi);
      console.log('最终结果:', result);
      console.log('用户权限列表:', this.$store.getters.permissions);
      console.log('用户角色列表:', this.$store.getters.roles);

      return result;
    }
  },
  methods: {
    checkPermi(permission) {
      return checkPermi(permission);
    },
    gotoEms() {
      uni.navigateTo({
        url: "/subpackages/ems/index",
      });
    },
    gotoVehicle() {
      console.log('跳转到车辆管理...');

      if (this.hasVehiclePermission) {
        uni.navigateTo({
          url: "/subpackages/vehicle/index",
          success: () => {
            console.log('成功跳转到车辆管理页面');
          },
          fail: (err) => {
            console.error('跳转到车辆管理页面失败:', err);
            this.$modal.showToast('页面跳转失败');
          }
        });
      } else {
        console.log('无车辆管理权限');
        this.$modal.showToast('您没有访问该页面的权限');
      }
    },

    gotoVisitor() {
      uni.navigateTo({
        url: "/subpackages/visitor/registration",
        fail: (err) => {
          console.error('跳转到访客管理页面失败:', err);
          this.$modal.showToast('页面跳转失败');
        }
      });
    },

    gotoCharts() {
      uni.navigateTo({
        url: "/subpackages/charts/index",
        fail: (err) => {
          console.error('跳转到数据图表页面失败:', err);
          this.$modal.showToast('页面跳转失败');
        }
      });
    },
  },
}
</script>

<style lang="scss">
/* #ifndef APP-NVUE */
page {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #f8f9fa;
  min-height: 100%;
  height: auto;
}

view {
  font-size: 14px;
  line-height: inherit;
}

/* #endif */

.work-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

/* 欢迎横幅样式 */
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 30rpx;
  margin: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);

  .banner-content {
    text-align: center;
    color: white;

    .banner-title {
      font-size: 48rpx;
      font-weight: 600;
      margin-bottom: 16rpx;
    }

    .banner-subtitle {
      font-size: 28rpx;
      opacity: 0.9;
      line-height: 1.4;
    }
  }
}

/* 功能模块样式 */
.modules-container {
  padding: 0 30rpx 30rpx;
}

.modules-grid {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.module-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  border: 1px solid #e9ecef;

  &:active {
    transform: scale(0.98);
  }

  .module-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 20rpx;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 30rpx;
    border: 1px solid #e9ecef;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  }

  .module-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12rpx;

    .module-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
      line-height: 1.3;
    }

    .module-desc {
      font-size: 26rpx;
      color: #666;
      line-height: 1.4;
      background-color: rgba(248, 249, 250, 0.8);
      padding: 6rpx 12rpx;
      border-radius: 12rpx;
      display: inline-block;
      width: fit-content;
    }
  }

  .module-arrow {
    margin-left: 20rpx;
    background-color: #f8f9fa;
    padding: 12rpx;
    border-radius: 12rpx;
    border: 1px solid #e9ecef;
  }
}

/* 设备控制样式 */
.device-control-container {
  padding: 30rpx;
  background: rgba(255, 255, 255, 0.95);
  margin: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.device-info {
  margin-bottom: 30rpx;
}

.device-item {
  display: flex;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border: 1px solid #e9ecef;
}

.label {
  font-weight: 600;
  min-width: 200rpx;
  color: #333;
  font-size: 28rpx;
}

.device-actions {
  display: flex;
  justify-content: flex-start;
  margin-top: 30rpx;
  gap: 20rpx;
}

.refresh-btn {
  display: flex;
  justify-content: center;
  margin-top: 30rpx;
  
  button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 20rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
    transition: all 0.2s ease;
    
    &:active {
      transform: scale(0.95);
    }
  }
}
</style>
