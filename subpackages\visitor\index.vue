<template>
  <view class="visitor-container">
    <!-- 欢迎横幅 -->
    <view class="welcome-banner">
      <view class="banner-content">
        <view class="banner-title">访客管理</view>
        <view class="banner-subtitle">访客登记与管理系统</view>
      </view>
    </view>

    <!-- 功能选择 -->
    <view class="modules-container">
      <view class="modules-grid">
        <!-- 访客登记 -->
        <view class="module-item" @click="goToRegistration">
          <view class="module-icon">
            <uni-icons type="person-add" size="32" color="#67C23A"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">访客登记</text>
            <text class="module-desc">新访客信息登记</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 访客列表 -->
        <view class="module-item" @click="goToList">
          <view class="module-icon">
            <uni-icons type="list" size="32" color="#409EFF"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">访客列表</text>
            <text class="module-desc">查看和管理访客记录</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 今日访客 -->
        <view class="module-item" @click="goToTodayList">
          <view class="module-icon">
            <uni-icons type="calendar" size="32" color="#E6A23C"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">今日访客</text>
            <text class="module-desc">今日访客统计</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>

        <!-- 访客统计 -->
        <view class="module-item" @click="goToStatistics">
          <view class="module-icon">
            <uni-icons type="bars" size="32" color="#F56C6C"></uni-icons>
          </view>
          <view class="module-info">
            <text class="module-title">访客统计</text>
            <text class="module-desc">访客数据统计分析</text>
          </view>
          <view class="module-arrow">
            <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
          </view>
        </view>
      </view>
    </view>

    <!-- 快速统计 -->
    <view class="stats-container">
      <uni-card title="今日统计" :is-shadow="false">
        <view class="stats-grid">
          <view class="stat-item">
            <view class="stat-number">{{ todayStats.total }}</view>
            <view class="stat-label">今日访客</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ todayStats.checkedIn }}</view>
            <view class="stat-label">已签入</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ todayStats.checkedOut }}</view>
            <view class="stat-label">已签出</view>
          </view>
          <view class="stat-item">
            <view class="stat-number">{{ todayStats.inBuilding }}</view>
            <view class="stat-label">在楼内</view>
          </view>
        </view>
      </uni-card>
    </view>
  </view>
</template>

<script>
import { getTodayRegistrations, getCurrentVisitors, getDailyStatistics } from '@/api/system/visitor'

export default {
  data() {
    return {
      todayStats: {
        total: 0,
        checkedIn: 0,
        checkedOut: 0,
        inBuilding: 0
      }
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '访客管理'
    });
    this.loadTodayStats();
  },
  onShow() {
    // 页面显示时刷新统计数据
    this.loadTodayStats();
  },
  methods: {
    // 跳转到访客登记页面
    goToRegistration() {
      uni.navigateTo({
        url: '/subpackages/visitor/registration',
        fail: (err) => {
          console.error('跳转到访客登记页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 跳转到访客列表页面
    goToList() {
      uni.navigateTo({
        url: '/subpackages/visitor/list',
        fail: (err) => {
          console.error('跳转到访客列表页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 跳转到今日访客页面
    goToTodayList() {
      const today = new Date().toISOString().split('T')[0];
      uni.navigateTo({
        url: `/subpackages/visitor/list?date=${today}`,
        fail: (err) => {
          console.error('跳转到今日访客页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 跳转到访客统计页面
    goToStatistics() {
      uni.navigateTo({
        url: '/subpackages/visitor/statistics',
        fail: (err) => {
          console.error('跳转到访客统计页面失败:', err);
          this.$modal.showError('页面跳转失败');
        }
      });
    },

    // 加载今日统计数据
    async loadTodayStats() {
      try {
        const today = new Date().toISOString().split('T')[0];

        // 获取今日统计数据
        const statsResponse = await getDailyStatistics(today);
        if (statsResponse.code === 200 && statsResponse.data) {
          this.todayStats = {
            total: statsResponse.data.totalRegistrations || 0,
            checkedIn: statsResponse.data.checkedInCount || 0,
            checkedOut: statsResponse.data.checkedOutCount || 0,
            inBuilding: (statsResponse.data.checkedInCount || 0) - (statsResponse.data.checkedOutCount || 0)
          };
        } else {
          // 如果统计接口失败，尝试获取今日登记数据
          const todayResponse = await getTodayRegistrations();
          if (todayResponse.code === 200 && todayResponse.data) {
            const registrations = todayResponse.data;
            this.todayStats.total = registrations.length;

            // 获取当前在楼访客
            const currentResponse = await getCurrentVisitors();
            if (currentResponse.code === 200 && currentResponse.data) {
              this.todayStats.inBuilding = currentResponse.data.length;
            }
          }
        }
      } catch (error) {
        console.error('加载今日统计失败:', error);
        // 静默处理错误，不影响页面显示
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.visitor-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px 20px;
  color: white;
  
  .banner-content {
    text-align: center;
    
    .banner-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .banner-subtitle {
      font-size: 14px;
      opacity: 0.9;
    }
  }
}

.modules-container {
  padding: 20px;
  
  .modules-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
    
    .module-item {
      background: white;
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      &:active {
        transform: scale(0.98);
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      }
      
      .module-icon {
        width: 50px;
        height: 50px;
        border-radius: 25px;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
      }
      
      .module-info {
        flex: 1;
        
        .module-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          display: block;
          margin-bottom: 4px;
        }
        
        .module-desc {
          font-size: 12px;
          color: #666;
          display: block;
        }
      }
      
      .module-arrow {
        opacity: 0.5;
      }
    }
  }
}

.stats-container {
  padding: 0 20px 30px;
  
  :deep(.uni-card) {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .uni-card__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 12px 12px 0 0;
      
      .uni-card__header-title {
        color: white;
        font-weight: bold;
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    padding: 10px 0;
    
    .stat-item {
      text-align: center;
      
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #667eea;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 12px;
        color: #666;
      }
    }
  }
}
</style>
