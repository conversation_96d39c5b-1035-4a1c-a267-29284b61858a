<template>
  <view class="announcement-list-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-title">公司公告</view>
      <view class="header-subtitle">查看所有公司公告信息</view>
    </view>

    <!-- 公告列表 -->
    <view class="announcement-list">
      <view 
        class="announcement-item" 
        v-for="item in announcements" 
        :key="item.id"
        @click="viewDetail(item)"
      >
        <view class="announcement-content">
          <view class="announcement-title">
            {{item.title}}
            <view class="new-badge" v-if="item.isNew">新</view>
          </view>
          <view class="announcement-summary">{{item.summary}}</view>
          <view class="announcement-meta">
            <text class="announcement-date">{{item.date}}</text>
            <text class="announcement-author" v-if="item.author">{{item.author}}</text>
          </view>
        </view>
        <view class="announcement-arrow">
          <uni-icons type="arrowright" size="16" color="#ccc"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" v-if="announcements.length === 0">
      <uni-icons type="info" size="48" color="#ccc"></uni-icons>
      <text class="empty-text">暂无公告信息</text>
    </view>

    <!-- 加载状态 -->
    <view class="loading-state" v-if="loading">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      announcements: []
    }
  },
  methods: {
    viewDetail(item) {
      uni.navigateTo({
        url: `/subpackages/common/announcement/detail?id=${item.id}&title=${encodeURIComponent(item.title)}`
      })
    },
    
    async loadAnnouncements() {
      this.loading = true;
      try {
        // TODO: 调用公告接口
        // const res = await this.$api.getAnnouncementList()
        // this.announcements = res.data || []
      } catch (error) {
        console.error('获取公告列表失败:', error)
      } finally {
        this.loading = false;
      }
    }
  },
  
  onLoad() {
    // 页面加载时获取公告数据
    this.loadAnnouncements();
  },
  
  onPullDownRefresh() {
    this.loadAnnouncements().finally(() => {
      uni.stopPullDownRefresh();
    });
  }
}
</script>

<style lang="scss" scoped>
.announcement-list-container {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.page-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40rpx 30rpx;
  color: white;
  text-align: center;

  .header-title {
    font-size: 36rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
  }

  .header-subtitle {
    font-size: 24rpx;
    opacity: 0.9;
  }
}

.announcement-list {
  padding: 30rpx;
  background-color: #f8f9fa;
  min-height: 100vh;

  .announcement-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;

    &:active {
      transform: scale(0.98);
    }

    .announcement-content {
      flex: 1;

      .announcement-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 24rpx;
        line-height: 1.4;
        display: flex;
        align-items: center;
        gap: 10rpx;

        .new-badge {
          background: #ff4757;
          color: white;
          font-size: 20rpx;
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
          line-height: 1;
        }
      }

      .announcement-summary {
        font-size: 28rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 24rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
      }

      .announcement-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 20rpx;
        border-top: 1rpx solid #f0f0f0;

        .announcement-date {
          font-size: 22rpx;
          color: #999;
          background-color: #f8f9fa;
          padding: 6rpx 12rpx;
          border-radius: 12rpx;
        }

        .announcement-author {
          font-size: 24rpx;
          color: #999;
          background-color: #f8f9fa;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
        }
      }
    }

    .announcement-arrow {
      margin-left: 20rpx;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-top: 60rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  text-align: center;

  .empty-text {
    font-size: 28rpx;
    color: #999;
    margin-top: 30rpx;
  }
}

.loading-state {
  padding: 60rpx;
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
}
</style>
