<template>
  <view class="normal-login-container">
    <view class="logo-content align-center justify-center flex">
      <text class="title">高义综合管理</text>
    </view>
    <!-- 添加内部使用说明 -->
    <view class="internal-notice">
      <text class="notice-text">此小程序仅供公司内部员工使用</text>
    </view>
    <view class="login-form-content">
      <view class="input-item flex align-center">
        <view class="iconfont icon-user icon"></view>
        <input v-model="loginForm.username" class="input" type="text" placeholder="请输入账号" maxlength="30" />
      </view>
      <view class="input-item flex align-center">
        <view class="iconfont icon-password icon"></view>
        <input v-model="loginForm.password" type="password" class="input" placeholder="请输入密码" maxlength="20" />
      </view>
      <view class="input-item flex align-center" style="width: 60%;margin: 0px;" v-if="captchaEnabled">
        <view class="iconfont icon-code icon"></view>
        <input v-model="loginForm.code" type="number" class="input" placeholder="请输入验证码" maxlength="4" />
        <view class="login-code">
          <image :src="codeUrl" @click="getCode" class="login-code-img"></image>
        </view>
      </view>
      <!-- 优化后的选项区域 -->
      <view class="options-container">
        <!-- 记住我选项 -->
        <view class="option-item flex align-center" @click="toggleRememberLogin">
          <checkbox-group @change="handleRememberChange" @click.stop class="checkbox-wrapper">
            <checkbox :checked="rememberLogin" />
          </checkbox-group>
          <text class="option-text">记住我</text>
        </view>
        
        <!-- 用户协议选项 -->
        <view class="option-item flex align-center" @click="toggleAgreement">
          <checkbox-group @change="handleAgreementChange" @click.stop class="checkbox-wrapper">
            <checkbox :checked="isAgreePolicy" />
          </checkbox-group>
          <view class="agreement-content">
            <text class="option-text">同意</text>
            <text @click.stop="handleUserAgrement" class="text-blue agreement-link">《用户协议》</text>
            <text class="option-text">和</text>
            <text @click.stop="handlePrivacy" class="text-blue agreement-link">《隐私协议》</text>
          </view>
        </view>
      </view>
      <view class="action-btn">
        <button @click="handleLogin" class="login-btn cu-btn block bg-blue lg round">登录</button>
      </view>
      <view class="action-btn" style="margin-top: 20px;">
        <button @click="handleVisitorRegistration" class="cu-btn block line-blue lg round">来访人员登记</button>
      </view>
    </view>
  </view>
</template>

<script>
  import { getCodeImg } from '@/api/login'
  import config from '@/config'

  export default {
    data() {
      return {
        codeUrl: "",
        captchaEnabled: false,
        isAgreePolicy: false,
        rememberLogin: false,
        loginForm: {
          username: "",
          password: "",
          code: "",
          uuid: ''
        }
      }
    },
    created() {
      console.log('后端地址:', config.baseUrl)
      this.getCode()
      this.loadSavedLoginInfo()
    },
    methods: {
      // 隐私协议
      handlePrivacy() {
        this.$tab.navigateTo('/subpackages/common/privacy')
      },
      // 用户协议
      handleUserAgrement() {
        this.$tab.navigateTo('/subpackages/common/agreement')
      },
      // 协议勾选状态变化
      handleAgreementChange(e) {
        this.isAgreePolicy = e.detail.value.length > 0
      },
      // 记住登录状态变化
      handleRememberChange(e) {
        this.rememberLogin = e.detail.value.length > 0
      },
      // 切换记住登录状态
      toggleRememberLogin() {
        this.rememberLogin = !this.rememberLogin
      },
      // 切换协议同意状态
      toggleAgreement() {
        this.isAgreePolicy = !this.isAgreePolicy
      },
      // 加载保存的登录信息
      loadSavedLoginInfo() {
        const savedUsername = uni.getStorageSync('saved_username')
        const savedPassword = uni.getStorageSync('saved_password')
        const rememberLogin = uni.getStorageSync('remember_login')
        
        if (rememberLogin) {
          this.rememberLogin = true
          this.loginForm.username = savedUsername || ''
          this.loginForm.password = savedPassword || ''
          this.isAgreePolicy = true // 如果记住了登录信息，默认同意协议
          
          // 如果有完整的登录信息且有有效token，尝试自动登录
          if (savedUsername && savedPassword && this.$store.state.user.token) {
            this.autoLogin()
          }
        }
      },
      // 自动登录
      async autoLogin() {
        try {
          await this.$store.dispatch('user/GetInfo')
          this.$tab.reLaunch('/pages/index')
        } catch (error) {
          console.log('自动登录失败，需要手动登录')
        }
      },
      // 保存登录信息
      saveLoginInfo() {
        if (this.rememberLogin) {
          uni.setStorageSync('saved_username', this.loginForm.username)
          uni.setStorageSync('saved_password', this.loginForm.password)
          uni.setStorageSync('remember_login', true)
        } else {
          uni.removeStorageSync('saved_username')
          uni.removeStorageSync('saved_password')
          uni.removeStorageSync('remember_login')
        }
      },
      // 获取图形验证码
      getCode() {
        getCodeImg().then(res => {
          this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
          if (this.captchaEnabled) {
            this.codeUrl = 'data:image/gif;base64,' + res.img
            this.loginForm.uuid = res.uuid
          }
        })
      },
      // 登录方法
      async handleLogin() {
        if (!this.isAgreePolicy) {
          this.$modal.msg("请阅读并同意用户协议和隐私协议")
          return
        }
        if (this.loginForm.username === "") {
          this.$modal.msg("请输入您的账号")
          return
        }
        if (this.loginForm.password === "") {
          this.$modal.msg("请输入您的密码")
          return
        }
        if (this.loginForm.code === "" && this.captchaEnabled) {
          this.$modal.msg("请输入验证码")
          return
        }
        this.$modal.loading("登录中，请耐心等待...")
        this.pwdLogin()
      },
      // 密码登录
      async pwdLogin() {
        try {
          await this.$store.dispatch('user/Login', this.loginForm);
          this.$modal.closeLoading();
          await this.loginSuccess();
        } catch (error) {
          console.error('登录失败:', error);
          this.$modal.closeLoading();
          // 刷新验证码
          if (this.captchaEnabled) {
            this.getCode();
          }
          // 不再显示错误提示，因为api层会处理
        }
      },
      // 登录成功后，处理函数
      async loginSuccess() {
        try {
          // 保存登录信息
          this.saveLoginInfo();
          await this.$store.dispatch('user/GetInfo');
          this.$tab.reLaunch('/pages/index');
        } catch (error) {
          console.error('获取用户信息失败:', error);
          this.$modal.closeLoading();
          this.$modal.msg('获取用户信息失败');
        }
      },
      // 处理来访人员登记点击事件
      handleVisitorRegistration() {
        this.$tab.navigateTo('/subpackages/visitor/registration');
      }
    }
  }
</script>

<style lang="scss">
  page {
    background-color: #ffffff;
  }

  .normal-login-container {
    width: 100%;

    .logo-content {
      width: 100%;
      font-size: 21px;
      text-align: center;
      padding-top: 15%;

      image {
        border-radius: 4px;
      }

      .title {
        margin-left: 10px;
      }
    }

    // 添加内部使用说明的样式
    .internal-notice {
      text-align: center;
      margin-top: 20px;
      
      .notice-text {
        font-size: 14px;
        color: #999;
        background-color: #f8f9fa;
        padding: 8px 16px;
        border-radius: 12px;
        display: inline-block;
        border: 1px solid #e9ecef;
      }
    }

    .login-form-content {
      text-align: center;
      margin: 20px auto;
      margin-top: 15%;
      width: 80%;

      .input-item {
        margin: 20px auto;
        background-color: #f5f6f7;
        height: 45px;
        border-radius: 20px;

        .icon {
          font-size: 38rpx;
          margin-left: 10px;
          color: #999;
        }

        .input {
          width: 100%;
          font-size: 14px;
          line-height: 20px;
          text-align: left;
          padding-left: 15px;
        }
      }

      // 优化后的选项容器样式
      .options-container {
        margin: 20px auto;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 12px;
        border: 1px solid #e9ecef;
        
        .option-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          padding: 4px 8px;
          cursor: pointer;
          min-height: 36px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .checkbox-wrapper {
            margin-right: 8px;
            display: flex;
            align-items: center;
            padding: 2px;
            
            checkbox {
              transform: scale(1);
            }
          }
          
          .option-text {
            font-size: 14px;
            color: #333;
            margin: 0 2px;
            line-height: 1.2;
            user-select: none;
          }
          
          .agreement-content {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            line-height: 1.2;
            
            .option-text {
              user-select: none;
              font-size: 14px;
            }
          }
          
          .agreement-link {
            margin: 0 1px;
            line-height: 1.2;
            text-decoration: underline;
            font-size: 14px;
            padding: 1px 2px;
          }
        }
      }

      .login-btn {
        margin-top: 40px;
        height: 45px;
      }

      .login-code {
        height: 38px;
        float: right;

        .login-code-img {
          height: 38px;
          position: absolute;
          margin-left: 10px;
          width: 200rpx;
        }
      }
    }
  }
</style>
