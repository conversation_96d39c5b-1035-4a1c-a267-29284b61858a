<template>
  <view class="announcement-detail-container">
    <!-- 公告标题区域 -->
    <view class="announcement-header">
      <view class="announcement-title">{{announcement.title}}</view>
      <view class="announcement-meta">
        <view class="meta-item">
          <uni-icons type="calendar" size="16" color="#999"></uni-icons>
          <text>{{announcement.date}}</text>
        </view>
        <view class="meta-item" v-if="announcement.author">
          <uni-icons type="person" size="16" color="#999"></uni-icons>
          <text>{{announcement.author}}</text>
        </view>
        <view class="new-badge" v-if="announcement.isNew">新</view>
      </view>
    </view>

    <!-- 公告内容 -->
    <view class="announcement-content">
      <view class="content-text">{{announcement.content}}</view>

      <!-- 附件列表 -->
      <view class="attachment-section" v-if="announcement.attachments && announcement.attachments.length > 0">
        <view class="section-title">相关附件</view>
        <view class="attachment-list">
          <view
            class="attachment-item"
            v-for="(attachment, index) in announcement.attachments"
            :key="index"
            @click="downloadAttachment(attachment)"
          >
            <uni-icons type="paperclip" size="20" color="#007AFF"></uni-icons>
            <text class="attachment-name">{{attachment.name}}</text>
            <text class="attachment-size">{{attachment.size}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn secondary" @click="goBack">返回</button>
      <button class="action-btn primary" @click="shareAnnouncement">分享</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading-overlay" v-if="loading">
      <uni-load-more status="loading"></uni-load-more>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      announcementId: '',
      announcement: {
        id: '',
        title: '',
        content: '',
        date: '',
        author: '',
        isNew: false,
        attachments: []
      }
    }
  },

  methods: {
    async loadAnnouncementDetail() {
      this.loading = true;
      try {
        // TODO: 调用公告详情接口
        // const res = await this.$api.getAnnouncementDetail(this.announcementId)
        // this.announcement = res.data || {}
      } catch (error) {
        console.error('获取公告详情失败:', error)
        this.$modal.showError('获取公告详情失败')
      } finally {
        this.loading = false;
      }
    },

    downloadAttachment(attachment) {
      this.$modal.showSuccess(`下载 ${attachment.name}`);
      // 这里可以实现文件下载逻辑
    },

    shareAnnouncement() {
      uni.share({
        provider: 'weixin',
        scene: 'WXSceneSession',
        type: 0,
        href: '',
        title: this.announcement.title,
        summary: this.announcement.content.substring(0, 100) + '...',
        success: () => {
          this.$modal.showSuccess('分享成功');
        },
        fail: () => {
          this.$modal.showError('分享失败');
        }
      });
    },

    goBack() {
      uni.navigateBack();
    }
  },

  onLoad(options) {
    this.announcementId = options.id || '1';
    this.loadAnnouncementDetail();
  }
}
</script>

<style lang="scss" scoped>
.announcement-detail {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 30rpx;
  padding-bottom: 140rpx;
}

.announcement-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;

  .announcement-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    margin-bottom: 24rpx;
  }

  .announcement-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .meta-left {
      display: flex;
      flex-direction: column;
      gap: 12rpx;

      .announcement-author {
        font-size: 26rpx;
        color: #666;
        background-color: #f8f9fa;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
        display: inline-block;
        width: fit-content;
      }

      .announcement-date {
        font-size: 24rpx;
        color: #999;
        background-color: #f8f9fa;
        padding: 6rpx 12rpx;
        border-radius: 12rpx;
        display: inline-block;
        width: fit-content;
      }
    }

    .new-badge {
      background: #ff4757;
      color: white;
      font-size: 20rpx;
      padding: 8rpx 12rpx;
      border-radius: 12rpx;
      line-height: 1;
      box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
    }
  }
}

.announcement-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;

  .content-text {
    font-size: 28rpx;
    line-height: 1.8;
    color: #333;
    white-space: pre-line;
  }

  .attachment-section {
    margin-top: 40rpx;
    padding-top: 30rpx;
    border-top: 1px solid #f0f0f0;

    .section-title {
      font-size: 30rpx;
      font-weight: 600;
      color: #333;
      margin-bottom: 24rpx;
    }

    .attachment-list {
      .attachment-item {
        display: flex;
        align-items: center;
        gap: 12rpx;
        padding: 24rpx 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        margin-bottom: 16rpx;
        transition: all 0.2s ease;
        border: 1px solid #e9ecef;

        &:active {
          background-color: #e9ecef;
          transform: scale(0.98);
        }

        .attachment-name {
          flex: 1;
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }

        .attachment-size {
          font-size: 22rpx;
          color: #999;
          background-color: rgba(255, 255, 255, 0.8);
          padding: 4rpx 8rpx;
          border-radius: 8rpx;
        }
      }
    }
  }
}

.action-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 30rpx;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

  .action-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 20rpx;
    font-size: 28rpx;
    font-weight: 500;
    border: none;
    transition: all 0.2s ease;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

    &.secondary {
      background: #f8f9fa;
      color: #666;
      border: 1px solid #e9ecef;
    }

    &.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 249, 250, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  backdrop-filter: blur(10rpx);
}
</style>
