import request from '@/utils/request'

/** 查询访客列表 */
export function listVisitor(query) {
  return request({
    url: '/asc/visitor/list',
    method: 'get',
    params: query
  })
}

/** 获取访客详细信息 */
export function getVisitor(id) {
  return request({
    url: `/asc/visitor/${id}`,
    method: 'get'
  })
}

/** 根据身份证号查询访客信息 */
export function getVisitorByIdCard(idCard) {
  return request({
    url: `/asc/visitor/idcard/${idCard}`,
    method: 'get'
  })
}

/** 新增访客登记 */
export function addVisitor(data) {
  return request({
    url: '/asc/visitor',
    method: 'post',
    data: data
  })
}

/** 修改访客信息 */
export function updateVisitor(data) {
  return request({
    url: '/asc/visitor',
    method: 'put',
    data: data
  })
}

/** 删除访客 */
export function delVisitor(ids) {
  return request({
    url: `/asc/visitor/${ids}`,
    method: 'delete'
  })
}

/** 访客签出 */
export function checkoutVisitor(id) {
  return request({
    url: `/asc/visitor/checkout/${id}`,
    method: 'put'
  })
}

/** 打印访客凭证 */
export function printVisitorTicket(id) {
  return request({
    url: `/asc/visitor/print/${id}`,
    method: 'post'
  })
}

/** 重新打印访客凭证 */
export function reprintVisitorTicket(id) {
  return request({
    url: `/asc/visitor/reprint/${id}`,
    method: 'post'
  })
}

/** 导出访客数据 */
export function exportVisitor(query) {
  return request({
    url: '/asc/visitor/export',
    method: 'post',
    params: query
  })
}
