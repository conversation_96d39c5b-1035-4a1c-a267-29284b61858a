import request from '@/utils/request'

// 访客基础信息接口
export function listVisitorBasicInfo(query) {
  return request({
    url: '/asc/visitor/basics/list',
    method: 'get',
    params: query
  })
}

export function getVisitorBasicInfo(visitorId) {
  return request({
    url: '/asc/visitor/basics/' + visitorId,
    method: 'get'
  })
}

export function getVisitorByIdCard(idCardNumber) {
  return request({
    url: '/asc/visitor/basics/idcard/' + idCardNumber,
    method: 'get'
  })
}

export function addVisitorBasicInfo(data) {
  return request({
    url: '/asc/visitor/basics',
    method: 'post',
    data: data
  })
}

export function updateVisitorBasicInfo(data) {
  return request({
    url: '/asc/visitor/basics',
    method: 'put',
    data: data
  })
}

// 访客登记信息接口
export function listRegistrationInfo(query) {
  return request({
    url: '/asc/visitor/registration/list',
    method: 'get',
    params: query
  })
}

export function getRegistrationInfo(registrationId) {
  return request({
    url: '/asc/visitor/registration/' + registrationId,
    method: 'get'
  })
}

/** 提交访客登记（支持多人登记，包含随行人员） */
export function submitVisitorRegistration(data) {
  return request({
    url: '/asc/visitor/registration',
    method: 'post',
    data: data
  })
}

export function updateRegistrationInfo(data) {
  return request({
    url: '/asc/visitor/registration',
    method: 'put',
    data: data
  })
}

// 查询今日登记信息
export function getTodayRegistrations() {
  return request({
    url: '/asc/visitor/registration/today',
    method: 'get'
  })
}

// 访客来访记录接口
export function listVisitRecords(query) {
  return request({
    url: '/asc/visitor/visit/list',
    method: 'get',
    params: query
  })
}

export function getCurrentVisits(query) {
  return request({
    url: '/asc/visitor/visit/current',
    method: 'get',
    params: query
  })
}

// 访客进出记录管理接口
export function listAccessLog(query) {
  return request({
    url: '/asc/visitor/access/list',
    method: 'get',
    params: query
  })
}

export function getCurrentVisitors() {
  return request({
    url: '/asc/visitor/access/current',
    method: 'get'
  })
}

// 统计接口
export function getDailyStatistics(date) {
  return request({
    url: '/asc/visitor/statistics/daily',
    method: 'get',
    params: { date }
  })
}

export function getVisitorStatistics() {
  return request({
    url: '/asc/visitor/basics/statistics',
    method: 'get'
  })
}
