<script>
  import config from './config'
  import store from '@/store'
  import { getToken } from '@/utils/auth'

  export default {
    onLaunch: function() {
      this.initApp()
    },
    methods: {
      // 初始化应用
      initApp() {
        // 初始化应用配置
        this.initConfig()
        // 检查用户登录状态
        //#ifdef H5
        this.checkLogin()
        //#endif
      },
      initConfig() {
        this.globalData.config = config
      },
      async checkLogin() {
        const token = getToken()
        const rememberLogin = uni.getStorageSync('remember_login')
        
        if (token && rememberLogin) {
          // 有token且记住登录，尝试获取用户信息验证token有效性
          try {
            await store.dispatch('user/GetInfo')
            // token有效，跳转到首页
            this.$tab.reLaunch('/pages/index')
          } catch (error) {
            // token无效，清除登录信息，跳转到登录页
            console.log('Token已过期，需要重新登录')
            store.dispatch('user/LogOut')
            this.$tab.reLaunch('/pages/login')
          }
        } else if (!token) {
          // 没有token，跳转到登录页
          this.$tab.reLaunch('/pages/login')
        }
      }
    }
  }
</script>

<style lang="scss">
  @import '@/static/scss/index.scss'
</style>
>