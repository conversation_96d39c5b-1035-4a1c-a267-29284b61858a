<template>
  <view class="statistics-container">
    <!-- 时间选择 -->
    <view class="time-selector">
      <uni-data-checkbox 
        v-model="timeRange"
        :localdata="timeRangeOptions"
        mode="button"
        @change="onTimeRangeChange"
      />
    </view>

    <!-- 统计卡片 -->
    <view class="stats-cards">
      <view class="stat-card">
        <view class="stat-number">{{ statistics.totalVisitors }}</view>
        <view class="stat-label">总访客数</view>
        <view class="stat-icon">
          <uni-icons type="person" size="24" color="#667eea"></uni-icons>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-number">{{ statistics.totalRegistrations }}</view>
        <view class="stat-label">总登记数</view>
        <view class="stat-icon">
          <uni-icons type="list" size="24" color="#67C23A"></uni-icons>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-number">{{ statistics.currentInBuilding }}</view>
        <view class="stat-label">在楼访客</view>
        <view class="stat-icon">
          <uni-icons type="home" size="24" color="#E6A23C"></uni-icons>
        </view>
      </view>
      
      <view class="stat-card">
        <view class="stat-number">{{ statistics.avgVisitDuration }}</view>
        <view class="stat-label">平均访问时长</view>
        <view class="stat-icon">
          <uni-icons type="clock" size="24" color="#F56C6C"></uni-icons>
        </view>
      </view>
    </view>

    <!-- 访问目的统计 -->
    <uni-card title="访问目的分布" :is-shadow="false" margin="15px 0">
      <view class="purpose-stats">
        <view v-for="(item, index) in purposeStats" :key="index" class="purpose-item">
          <view class="purpose-info">
            <text class="purpose-name">{{ item.purpose }}</text>
            <text class="purpose-count">{{ item.count }}人次</text>
          </view>
          <view class="purpose-bar">
            <view class="purpose-progress" :style="{ width: item.percentage + '%' }"></view>
          </view>
        </view>
      </view>
    </uni-card>

    <!-- 部门访问统计 -->
    <uni-card title="部门访问排行" :is-shadow="false" margin="15px 0">
      <view class="department-stats">
        <view v-for="(item, index) in departmentStats" :key="index" class="department-item">
          <view class="rank-number">{{ index + 1 }}</view>
          <view class="department-info">
            <text class="department-name">{{ item.department }}</text>
            <text class="department-count">{{ item.count }}人次</text>
          </view>
        </view>
      </view>
    </uni-card>

    <!-- 时间分布统计 -->
    <uni-card title="访问时间分布" :is-shadow="false" margin="15px 0">
      <view class="time-stats">
        <view v-for="(item, index) in timeStats" :key="index" class="time-item">
          <text class="time-label">{{ item.timeRange }}</text>
          <view class="time-bar">
            <view class="time-progress" :style="{ width: item.percentage + '%' }"></view>
          </view>
          <text class="time-count">{{ item.count }}</text>
        </view>
      </view>
    </uni-card>
  </view>
</template>

<script>
import { getDailyStatistics, getMonthlyStatistics, listRegistrationInfo } from '@/api/system/visitor'

export default {
  data() {
    return {
      timeRange: 'today',
      timeRangeOptions: [
        { value: 'today', text: '今日' },
        { value: 'week', text: '本周' },
        { value: 'month', text: '本月' },
        { value: 'year', text: '本年' }
      ],
      statistics: {
        totalVisitors: 0,
        totalRegistrations: 0,
        currentInBuilding: 0,
        avgVisitDuration: '0小时'
      },
      purposeStats: [],
      departmentStats: [],
      timeStats: []
    };
  },
  onLoad() {
    uni.setNavigationBarTitle({
      title: '访客统计'
    });
    this.loadStatistics();
  },
  methods: {
    // 时间范围变更
    onTimeRangeChange() {
      this.loadStatistics();
    },

    // 加载统计数据
    async loadStatistics() {
      try {
        uni.showLoading({ title: '加载中...' });
        
        // 根据时间范围获取不同的统计数据
        await Promise.all([
          this.loadBasicStats(),
          this.loadPurposeStats(),
          this.loadDepartmentStats(),
          this.loadTimeStats()
        ]);
        
      } catch (error) {
        console.error('加载统计数据失败:', error);
        this.$modal.showError('加载统计数据失败');
      } finally {
        uni.hideLoading();
      }
    },

    // 加载基础统计
    async loadBasicStats() {
      try {
        let response;
        const today = new Date().toISOString().split('T')[0];
        
        if (this.timeRange === 'today') {
          response = await getDailyStatistics(today);
        } else if (this.timeRange === 'month') {
          const yearMonth = today.substring(0, 7);
          response = await getMonthlyStatistics(yearMonth);
        }
        
        if (response && response.code === 200 && response.data) {
          this.statistics = {
            totalVisitors: response.data.totalVisitors || 0,
            totalRegistrations: response.data.totalRegistrations || 0,
            currentInBuilding: response.data.currentInBuilding || 0,
            avgVisitDuration: response.data.avgVisitDuration || '0小时'
          };
        }
      } catch (error) {
        console.error('加载基础统计失败:', error);
      }
    },

    // 加载访问目的统计
    async loadPurposeStats() {
      try {
        // 这里应该调用专门的统计API，暂时用模拟数据
        this.purposeStats = [
          { purpose: '商务洽谈', count: 45, percentage: 35 },
          { purpose: '技术交流', count: 32, percentage: 25 },
          { purpose: '参观访问', count: 28, percentage: 22 },
          { purpose: '会议参加', count: 15, percentage: 12 },
          { purpose: '其他', count: 8, percentage: 6 }
        ];
      } catch (error) {
        console.error('加载访问目的统计失败:', error);
      }
    },

    // 加载部门统计
    async loadDepartmentStats() {
      try {
        // 这里应该调用专门的统计API，暂时用模拟数据
        this.departmentStats = [
          { department: '技术部', count: 56 },
          { department: '销售部', count: 43 },
          { department: '市场部', count: 38 },
          { department: '人事部', count: 25 },
          { department: '财务部', count: 18 }
        ];
      } catch (error) {
        console.error('加载部门统计失败:', error);
      }
    },

    // 加载时间分布统计
    async loadTimeStats() {
      try {
        // 这里应该调用专门的统计API，暂时用模拟数据
        this.timeStats = [
          { timeRange: '08:00-10:00', count: 25, percentage: 20 },
          { timeRange: '10:00-12:00', count: 45, percentage: 36 },
          { timeRange: '12:00-14:00', count: 15, percentage: 12 },
          { timeRange: '14:00-16:00', count: 35, percentage: 28 },
          { timeRange: '16:00-18:00', count: 8, percentage: 6 }
        ];
      } catch (error) {
        console.error('加载时间分布统计失败:', error);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.statistics-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.time-selector {
  margin-bottom: 20px;
  
  :deep(.uni-data-checkbox) {
    .checklist-group {
      display: flex;
      gap: 10px;
      
      .checklist-box {
        border-radius: 15px;
        padding: 8px 16px;
        font-size: 12px;
        
        &.is--checked {
          background-color: #667eea;
          border-color: #667eea;
          color: white;
        }
      }
    }
  }
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
  
  .stat-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin-bottom: 5px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #666;
    }
    
    .stat-icon {
      position: absolute;
      top: 15px;
      right: 15px;
      opacity: 0.3;
    }
  }
}

.purpose-stats {
  .purpose-item {
    margin-bottom: 15px;
    
    .purpose-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      
      .purpose-name {
        font-size: 14px;
        color: #333;
      }
      
      .purpose-count {
        font-size: 12px;
        color: #666;
      }
    }
    
    .purpose-bar {
      height: 6px;
      background-color: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;
      
      .purpose-progress {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        transition: width 0.3s ease;
      }
    }
  }
}

.department-stats {
  .department-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
      border-bottom: none;
    }
    
    .rank-number {
      width: 30px;
      height: 30px;
      border-radius: 15px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      margin-right: 15px;
    }
    
    .department-info {
      flex: 1;
      
      .department-name {
        display: block;
        font-size: 14px;
        color: #333;
        margin-bottom: 2px;
      }
      
      .department-count {
        font-size: 12px;
        color: #666;
      }
    }
  }
}

.time-stats {
  .time-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    
    .time-label {
      width: 100px;
      font-size: 12px;
      color: #666;
    }
    
    .time-bar {
      flex: 1;
      height: 6px;
      background-color: #f0f0f0;
      border-radius: 3px;
      margin: 0 10px;
      overflow: hidden;
      
      .time-progress {
        height: 100%;
        background: linear-gradient(135deg, #67C23A 0%, #85CE61 100%);
        transition: width 0.3s ease;
      }
    }
    
    .time-count {
      width: 30px;
      text-align: right;
      font-size: 12px;
      color: #333;
    }
  }
}

// 卡片样式优化
:deep(.uni-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .uni-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    
    .uni-card__header-title {
      color: white;
      font-weight: bold;
    }
  }
  
  .uni-card__content {
    padding: 20px;
  }
}
</style>
