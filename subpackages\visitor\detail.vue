<template>
  <view class="detail-container">
    <view v-if="loading" class="loading-container">
      <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
    </view>
    
    <view v-else-if="visitorInfo" class="content">
      <!-- 访客基本信息 -->
      <uni-card title="访客信息" :is-shadow="false" margin="15px 0">
        <view class="info-item">
          <text class="label">姓名：</text>
          <text class="value">{{ visitorInfo.name }}</text>
        </view>
        <view class="info-item">
          <text class="label">身份证号：</text>
          <text class="value">{{ formatIdCard(visitorInfo.idCard) }}</text>
        </view>
        <view class="info-item">
          <text class="label">联系电话：</text>
          <text class="value">{{ visitorInfo.phone }}</text>
        </view>
        <view class="info-item" v-if="visitorInfo.company">
          <text class="label">所属公司：</text>
          <text class="value">{{ visitorInfo.company }}</text>
        </view>
      </uni-card>

      <!-- 来访信息 -->
      <uni-card title="来访信息" :is-shadow="false" margin="15px 0">
        <view class="info-item">
          <text class="label">来访目的：</text>
          <text class="value">{{ visitorInfo.purpose }}</text>
        </view>
        <view class="info-item">
          <text class="label">被访人：</text>
          <text class="value">{{ visitorInfo.visitTo }}</text>
        </view>
        <view class="info-item">
          <text class="label">被访部门：</text>
          <text class="value">{{ visitorInfo.visitToDept }}</text>
        </view>
        <view class="info-item" v-if="visitorInfo.expectedDuration">
          <text class="label">预计访问时长：</text>
          <text class="value">{{ visitorInfo.expectedDuration }}</text>
        </view>
        <view class="info-item" v-if="visitorInfo.remark">
          <text class="label">备注：</text>
          <text class="value">{{ visitorInfo.remark }}</text>
        </view>
      </uni-card>

      <!-- 访问状态 -->
      <uni-card title="访问状态" :is-shadow="false" margin="15px 0">
        <view class="status-item">
          <view class="status-badge" :class="statusClass">
            {{ statusText }}
          </view>
        </view>
        <view class="info-item" v-if="visitorInfo.checkInTime">
          <text class="label">签入时间：</text>
          <text class="value">{{ formatDateTime(visitorInfo.checkInTime) }}</text>
        </view>
        <view class="info-item" v-if="visitorInfo.checkOutTime">
          <text class="label">签出时间：</text>
          <text class="value">{{ formatDateTime(visitorInfo.checkOutTime) }}</text>
        </view>
      </uni-card>

      <!-- 随行人员 -->
      <uni-card v-if="companions.length > 0" title="随行人员" :is-shadow="false" margin="15px 0">
        <view v-for="(companion, index) in companions" :key="index" class="companion-item">
          <view class="companion-header">
            <text class="companion-title">随行人员 {{ index + 1 }}</text>
          </view>
          <view class="info-item">
            <text class="label">姓名：</text>
            <text class="value">{{ companion.name }}</text>
          </view>
          <view class="info-item">
            <text class="label">身份证号：</text>
            <text class="value">{{ formatIdCard(companion.idCard) }}</text>
          </view>
          <view class="info-item" v-if="companion.phone">
            <text class="label">联系电话：</text>
            <text class="value">{{ companion.phone }}</text>
          </view>
        </view>
      </uni-card>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button v-if="canCheckOut" class="checkout-btn" @click="handleCheckOut">
          访客签出
        </button>
        <button class="print-btn" @click="handlePrint">
          打印凭证
        </button>
      </view>
    </view>

    <view v-else class="error-container">
      <uni-icons type="info" size="60" color="#ccc"></uni-icons>
      <text class="error-text">访客信息不存在</text>
    </view>
  </view>
</template>

<script>
import { getRegistrationInfo } from '@/api/system/visitor'

export default {
  data() {
    return {
      loading: true,
      visitorInfo: null,
      companions: [],
      registrationId: null,
      loadingText: {
        contentdown: '上拉显示更多',
        contentrefresh: '正在加载...',
        contentnomore: '没有更多数据了'
      }
    };
  },
  computed: {
    canCheckOut() {
      return this.visitorInfo && this.visitorInfo.status === 'CHECKED_IN';
    },
    statusText() {
      if (!this.visitorInfo) return '';
      return this.getStatusText(this.visitorInfo.status);
    },
    statusClass() {
      if (!this.visitorInfo) return '';
      return this.getStatusClass(this.visitorInfo.status);
    }
  },
  onLoad(options) {
    if (options.id) {
      this.registrationId = options.id;
      this.loadVisitorDetail();
    } else {
      this.loading = false;
    }
  },
  methods: {
    // 加载访客详情
    async loadVisitorDetail() {
      try {
        this.loading = true;
        const response = await getRegistrationInfo(this.registrationId);
        
        if (response.code === 200 && response.data) {
          this.visitorInfo = response.data.mainVisitor || response.data;
          this.companions = response.data.companions || [];
          
          uni.setNavigationBarTitle({
            title: `${this.visitorInfo.name} - 访客详情`
          });
        } else {
          this.$modal.showError(response.msg || '获取访客信息失败');
        }
      } catch (error) {
        console.error('加载访客详情失败:', error);
        this.$modal.showError('加载失败，请检查网络连接');
      } finally {
        this.loading = false;
      }
    },

    // 处理签出
    async handleCheckOut() {
      try {
        const result = await this.$modal.confirm('确认为该访客办理签出吗？');
        if (!result) return;

        this.$modal.showLoading('正在办理签出...');
        // 这里调用签出API
        // const response = await checkoutVisitor(this.registrationId);
        
        this.$modal.showSuccess('签出成功');
        this.loadVisitorDetail(); // 刷新数据
      } catch (error) {
        console.error('签出失败:', error);
        this.$modal.showError('签出失败，请重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 处理打印
    async handlePrint() {
      try {
        this.$modal.showLoading('准备打印...');
        // 这里调用打印API
        this.$modal.showSuccess('打印任务已发送');
      } catch (error) {
        console.error('打印失败:', error);
        this.$modal.showError('打印失败，请重试');
      } finally {
        this.$modal.hideLoading();
      }
    },

    // 格式化身份证号
    formatIdCard(idCard) {
      if (!idCard) return '';
      return idCard.replace(/^(.{6}).*(.{4})$/, '$1****$2');
    },

    // 格式化日期时间
    formatDateTime(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
    },

    // 获取状态文本
    getStatusText(status) {
      switch (status) {
        case 'PENDING':
          return '待审核';
        case 'APPROVED':
          return '已审核';
        case 'CHECKED_IN':
          return '已签入';
        case 'CHECKED_OUT':
          return '已签出';
        case 'REJECTED':
          return '已拒绝';
        default:
          return '未知状态';
      }
    },

    // 获取状态样式类
    getStatusClass(status) {
      switch (status) {
        case 'PENDING':
          return 'status-pending';
        case 'APPROVED':
          return 'status-approved';
        case 'CHECKED_IN':
          return 'status-checked-in';
        case 'CHECKED_OUT':
          return 'status-checked-out';
        case 'REJECTED':
          return 'status-rejected';
        default:
          return 'status-unknown';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px;
}

.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
  
  .error-text {
    margin-top: 15px;
    color: #999;
    font-size: 14px;
  }
}

.info-item {
  display: flex;
  margin-bottom: 15px;
  
  .label {
    width: 100px;
    color: #666;
    font-size: 14px;
  }
  
  .value {
    flex: 1;
    color: #333;
    font-size: 14px;
    word-break: break-all;
  }
}

.status-item {
  margin-bottom: 15px;
  
  .status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    
    &.status-pending {
      background-color: #E6A23C;
      color: white;
    }
    
    &.status-approved {
      background-color: #67C23A;
      color: white;
    }
    
    &.status-checked-in {
      background-color: #409EFF;
      color: white;
    }
    
    &.status-checked-out {
      background-color: #909399;
      color: white;
    }
    
    &.status-rejected {
      background-color: #F56C6C;
      color: white;
    }
    
    &.status-unknown {
      background-color: #C0C4CC;
      color: white;
    }
  }
}

.companion-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fafafa;
  
  .companion-header {
    margin-bottom: 10px;
    
    .companion-title {
      font-weight: bold;
      color: #333;
      font-size: 14px;
    }
  }
}

.action-buttons {
  margin-top: 30px;
  display: flex;
  gap: 15px;
  
  .checkout-btn, .print-btn {
    flex: 1;
    height: 45px;
    border-radius: 22px;
    font-size: 14px;
    font-weight: bold;
    border: none;
    
    &:active {
      opacity: 0.8;
    }
  }
  
  .checkout-btn {
    background: linear-gradient(135deg, #F56C6C 0%, #FF8A80 100%);
    color: white;
  }
  
  .print-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
}

// 卡片样式优化
:deep(.uni-card) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .uni-card__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    
    .uni-card__header-title {
      color: white;
      font-weight: bold;
    }
  }
  
  .uni-card__content {
    padding: 20px;
  }
}
</style>
