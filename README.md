<p align="center">
  <img src="/static/logo200.png" alt="高义钢铁有限公司" width="150">
</p>

<h1 align="center">高义钢铁有限公司综合管理系统 UniApp 移动端 (v1.1.0)</h1>

[![Version](https://img.shields.io/badge/version-v1.1.0-blue)]() [![License](https://img.shields.io/badge/license-MIT-green)](LICENSE) [![UniApp](https://img.shields.io/badge/UniApp-Vue2-green)]() [![Platform](https://img.shields.io/badge/platform-微信小程序%20%7C%20H5%20%7C%20App-blue)]()

## 目录

- [项目简介](#项目简介)
- [主要功能](#主要功能)
- [技术栈](#技术栈)
- [项目结构](#项目结构)
- [核心模块说明](#核心模块说明)
- [分包架构](#分包架构)
- [环境搭建](#环境搭建)
- [开发 & 运行](#开发--运行)
- [打包 & 发布](#打包--发布)
- [编码规范](#编码规范)
- [贡献指南](#贡献指南)
- [许可证](#许可证)
- [联系方式](#联系方式)

## 项目简介

高义钢铁有限公司综合管理系统移动端项目，基于 UniApp (Vue2) 开发，实现一次开发，多端（微信小程序、H5、App）部署。为钢铁企业提供移动化的综合管理解决方案，涵盖能源管理、车辆管理、设备监控、用户管理等核心业务模块。

## 主要功能

### 🔐 用户认证与权限
- 用户登录/登出、验证码验证
- Token 自动管理与刷新
- 基于角色的权限控制
- 个人信息管理与头像上传

### ⚡ EMS 能源管理系统
- 能源组织架构管理
- 能源标签数据查询
- 能源消耗图表分析
- 实时能源监控

### 🚗 车辆管理系统
- 车辆信息登记与管理
- 车牌机设备管理
- 车辆进出记录
- 设备状态监控

### 📊 数据可视化
- 图表分析模块
- 能源消耗趋势图
- 设备运行状态图表
- 数据统计报表

### 👤 个人中心
- 个人信息编辑
- 密码修改
- 应用设置
- 帮助文档与常见问题
- 关于我们

### 📱 通用功能
- 网页浏览器
- 文本查看器
- 用户协议与隐私政策
- 公司公告管理

## 技术栈

### 前端框架
- **开发框架**：UniApp (Vue2)
- **UI 组件库**：uni-ui (uni-list、uni-forms、uni-data-checkbox、uni-easyinput 等)
- **图表组件**：qiun-data-charts (支持 uCharts 和 ECharts)
- **状态管理**：Vuex
- **路由管理**：UniApp 内置路由

### 开发语言
- **脚本语言**：JavaScript (ES6+)
- **样式语言**：SCSS
- **模板语言**：Vue Template

### 开发工具
- **IDE**：HBuilderX
- **构建工具**：UniApp CLI
- **版本控制**：Git

### 部署平台
- **微信小程序**：支持微信小程序平台
- **H5**：支持移动端 H5 页面
- **App**：支持 Android/iOS 原生应用

## 项目结构

```text
├── api/                    # 后端接口定义
│   ├── login.js           # 登录认证相关接口
│   ├── ems/               # EMS 能源管理接口
│   │   └── ems.js         # 能源组织、标签、图表接口
│   └── system/            # 系统管理接口
│       ├── device.js      # 车牌机设备管理接口
│       ├── user.js        # 用户信息管理接口
│       └── vehicle.js     # 车辆管理接口
├── store/                 # Vuex 状态管理
│   ├── modules/           # 模块化状态管理
│   │   └── user.js        # 用户状态模块
│   ├── getters.js         # 全局 getters
│   └── index.js           # Store 入口文件
├── utils/                 # 工具函数库
│   ├── request.js         # HTTP 请求封装
│   ├── auth.js            # Token 存取管理
│   ├── permission.js      # 前端权限控制
│   ├── upload.js          # 文件上传工具
│   ├── common.js          # 通用工具函数
│   ├── storage.js         # 本地存储管理
│   └── constant.js        # 常量定义
├── pages/                 # 主包页面
│   ├── login.vue          # 登录页面
│   ├── index.vue          # 首页
│   ├── work/              # 工作台模块
│   │   └── index.vue      # 工作台首页
│   └── mine/              # 个人中心
│       └── index.vue      # 个人中心首页
├── subpackages/           # 分包页面
│   ├── charts/            # 图表分析分包
│   │   ├── index.vue      # 图表分析页面
│   │   └── uni_modules/   # 图表组件模块
│   ├── ems/               # EMS 能源管理分包
│   │   ├── index.vue      # EMS 列表页面
│   │   └── detail.vue     # EMS 详情页面
│   ├── vehicle/           # 车辆管理分包
│   │   ├── index.vue      # 车辆管理首页
│   │   ├── info/          # 车辆信息管理
│   │   └── device/        # 车牌机设备管理
│   ├── user/              # 用户管理分包
│   │   ├── index.vue      # 用户中心
│   │   ├── info/          # 个人信息管理
│   │   ├── pwd/           # 密码修改
│   │   ├── setting/       # 应用设置
│   │   ├── help/          # 帮助文档
│   │   ├── about/         # 关于我们
│   │   └── avatar/        # 头像修改
│   ├── common/            # 通用功能分包
│   │   ├── webview/       # 网页浏览器
│   │   ├── textview/      # 文本查看器
│   │   ├── agreement.vue  # 用户协议
│   │   ├── privacy.vue    # 隐私政策
│   │   ├── announcement/  # 公告管理
│   │   └── uni_modules/   # 通用组件模块
│   └── visitor/           # 访客管理分包
│       └── registration.vue # 访客登记
├── plugins/               # 插件和扩展
│   ├── auth.js           # 认证插件
│   ├── modal.js          # 弹窗插件
│   ├── tab.js            # 标签页插件
│   └── index.js          # 插件入口
├── static/               # 静态资源
│   ├── images/           # 图片资源
│   │   └── tabbar/       # 底部导航图标
│   ├── font/             # 字体文件
│   │   ├── iconfont.css  # 图标字体样式
│   │   └── iconfont.ttf  # 图标字体文件
│   └── scss/             # 样式文件
│       ├── colorui.css   # ColorUI 样式库
│       ├── global.scss   # 全局样式
│       └── index.scss    # 样式入口
├── uni_modules/          # UniApp 官方组件模块
│   ├── uni-list/         # 列表组件
│   ├── uni-forms/        # 表单组件
│   ├── uni-data-checkbox/ # 数据选择器
│   ├── uni-easyinput/    # 输入框组件
│   ├── uni-icons/        # 图标组件
│   ├── uni-popup/        # 弹出层组件
│   ├── uni-search-bar/   # 搜索栏组件
│   └── uni-section/      # 分段器组件
├── docs/                 # 项目文档
│   └── api-docs.yaml     # API 文档
├── config.js             # 全局配置文件
├── manifest.json         # 应用配置清单
├── pages.json            # 页面路由配置
├── permission.js         # 权限控制入口
├── main.js               # 应用入口文件
├── App.vue               # 应用根组件
├── uni.scss              # 全局样式变量
└── README.md             # 项目说明文档
```

## 核心模块说明

### 🔌 API 接口层 (`api/`)
- **`login.js`**：用户登录、获取用户信息、登出等认证相关接口
- **`ems/ems.js`**：能源管理系统接口，包括组织列表、标签查询、图表数据获取
- **`system/device.js`**：车牌机设备的增删改查、状态管理接口
- **`system/vehicle.js`**：车辆信息的增删改查、车辆管理接口
- **`system/user.js`**：用户个人信息、密码修改、头像上传接口

### 🗃️ 状态管理 (`store/`)
- **`modules/user.js`**：用户登录状态、用户信息、权限数据管理
- **`getters.js`**：全局状态的计算属性，提供便捷的状态访问方式
- **`index.js`**：Vuex store 的入口文件，整合所有模块

### 🛠️ 工具函数 (`utils/`)
- **`request.js`**：HTTP 请求封装，自动注入 Token、统一错误处理、请求拦截
- **`auth.js`**：Token 的获取、存储、移除，登录状态管理
- **`permission.js`**：前端路由权限校验，页面访问控制
- **`upload.js`**：文件上传工具，支持图片上传
- **`common.js`**：通用工具函数，日期格式化、数据处理等
- **`storage.js`**：本地存储封装，统一的缓存管理

### 📱 页面配置 (`pages.json`)
- **主包页面**：登录、首页、工作台、个人中心等核心页面
- **分包配置**：charts、ems、vehicle、user、common、visitor 等业务模块
- **TabBar 配置**：底部导航栏 (首页、工作台、我的)
- **全局样式**：导航栏样式、下拉刷新配置
- **easycom 配置**：组件自动导入配置

### 🎨 样式系统 (`static/scss/`)
- **`global.scss`**：全局样式定义
- **`colorui.css`**：ColorUI 样式库
- **`index.scss`**：样式文件入口
- **`uni.scss`**：UniApp 全局样式变量

### 🔧 插件系统 (`plugins/`)
- **`auth.js`**：认证相关插件，登录状态检查
- **`modal.js`**：弹窗提示插件，统一的消息提示
- **`tab.js`**：标签页管理插件
- **`index.js`**：插件系统入口文件

## 分包架构

本项目采用 UniApp 分包架构，优化小程序包体积和加载性能：

### 主包 (Main Package)
- **大小限制**：2MB
- **包含内容**：核心页面（登录、首页、工作台、个人中心）、公共组件、工具函数
- **加载时机**：应用启动时加载

### 分包 (Sub Packages)
1. **charts 分包**：图表分析模块，按需加载
2. **ems 分包**：能源管理系统，独立业务模块
3. **vehicle 分包**：车辆管理系统，包含车辆信息和设备管理
4. **user 分包**：用户相关功能，个人信息、设置、帮助等
5. **common 分包**：通用功能，网页浏览、协议、公告等
6. **visitor 分包**：访客管理功能

### 组件模块 (uni_modules)
- **主包组件**：uni-list、uni-forms、uni-data-checkbox 等核心组件
- **分包组件**：qiun-data-charts（图表）、uni-card（卡片）等专用组件

## 环境搭建

### 开发环境要求
- **Node.js**：>= 12.0.0
- **HBuilderX**：>= 3.3.8 (推荐使用最新版)
- **微信开发者工具**：用于小程序调试

### 安装步骤
1. 克隆项目到本地
```bash
git clone [项目地址]
cd gaoyi_plat_weixin
```

2. 安装依赖（如果使用 CLI 开发）
```bash
npm install
```

3. 配置开发环境
- 在 HBuilderX 中导入项目
- 配置微信小程序 AppID（manifest.json）
- 配置后端 API 地址（config.js）

## 开发 & 运行

### HBuilderX 开发（推荐）
1. 在 HBuilderX 中打开项目
2. 选择运行平台：
   - **微信小程序**：运行 → 运行到小程序模拟器 → 微信开发者工具
   - **H5**：运行 → 运行到浏览器 → Chrome
   - **App**：运行 → 运行到手机或模拟器

### CLI 开发
```bash
# 微信小程序开发
npm run dev:mp-weixin

# H5 开发
npm run dev:h5

# App 开发
npm run dev:app-plus
```

### 开发调试
- **微信小程序**：使用微信开发者工具进行调试
- **H5**：使用浏览器开发者工具
- **App**：使用真机调试或模拟器

### 环境配置
- **开发环境**：修改 `config.js` 中的 `baseUrl` 为开发服务器地址
- **生产环境**：使用 `https://ems.sxgygt.com/prod-api` 作为 API 地址

## 打包 & 发布

### HBuilderX 打包
1. **微信小程序**：
   - 发行 → 小程序-微信
   - 配置小程序信息
   - 生成小程序代码包

2. **H5**：
   - 发行 → 网站-H5手机版
   - 配置域名和路径
   - 生成 H5 静态文件

3. **App**：
   - 发行 → 原生App-云打包
   - 配置证书和签名
   - 生成 APK/IPA 文件

### CLI 打包
```bash
# 微信小程序打包
npm run build:mp-weixin

# H5 打包
npm run build:h5

# App 打包
npm run build:app-plus
```

### 发布注意事项
- **微信小程序**：需要在微信公众平台提交审核
- **H5**：部署到服务器，配置 HTTPS
- **App**：需要应用商店审核（App Store、应用宝等）
- **版本管理**：更新 `manifest.json` 中的版本号

## 编码规范

### 文件命名
- **页面文件**：使用小写字母，多个单词用 `-` 连接（如：`user-info.vue`）
- **组件文件**：使用 PascalCase 命名（如：`UserCard.vue`）
- **工具文件**：使用小写字母，多个单词用驼峰命名（如：`commonUtils.js`）

### 代码风格
- **缩进**：使用 2 个空格
- **引号**：统一使用单引号
- **分号**：语句结尾不使用分号
- **换行**：每行代码不超过 120 个字符

### 目录结构规范
- **新页面**：必须在 `pages.json` 中注册，路径与文件目录保持一致
- **分包页面**：放在对应的 `subpackages/` 目录下
- **公共组件**：放在 `components/` 目录，优先使用 `uni_modules/`
- **静态资源**：图片放在 `static/images/`，样式放在 `static/scss/`

### API 调用规范
- **统一封装**：所有 API 调用必须使用 `utils/request.js` 封装
- **错误处理**：统一在 request 拦截器中处理
- **接口文档**：在 `api/` 目录下按模块组织接口

### 样式规范
- **预处理器**：使用 SCSS，变量定义在 `uni.scss`
- **全局样式**：定义在 `static/scss/global.scss`
- **组件样式**：使用 scoped 作用域
- **响应式**：使用 rpx 单位适配不同屏幕

### 组件使用规范
- **uni-ui 组件**：优先使用官方组件库
- **自定义组件**：遵循 Vue 组件开发规范
- **组件通信**：使用 props 和 events，避免直接操作 DOM

## 贡献指南

### 参与贡献
1. **Fork 项目**：点击右上角 Fork 按钮
2. **创建分支**：`git checkout -b feature/your-feature-name`
3. **提交代码**：`git commit -m 'Add some feature'`
4. **推送分支**：`git push origin feature/your-feature-name`
5. **提交 PR**：在 GitHub 上创建 Pull Request

### 问题反馈
- **Bug 报告**：请详细描述问题复现步骤、期望行为和实际行为
- **功能建议**：请说明功能需求和使用场景
- **问题标签**：使用合适的 Label 标记问题类型

### 开发规范
- 遵循项目的编码规范
- 添加必要的注释和文档
- 确保代码通过测试
- 保持提交信息清晰明确

## 许可证

本项目遵循 [MIT License](LICENSE)

## 联系方式

- **项目维护**：高义钢铁有限公司技术团队
- **官方网站**：[http://www.sxgygtyxgs.com](http://www.sxgygtyxgs.com)
- **技术支持**：请通过 GitHub Issues 提交问题
- **商务合作**：请通过官方网站联系我们

---

<p align="center">
  <strong>高义钢铁有限公司综合管理系统</strong><br>
  <em>让钢铁企业管理更智能、更高效</em>
</p>
